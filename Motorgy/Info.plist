<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key> NSAdvertisingAttributionReportEndpoint</key>
	<string>https://appsflyer-skadnetwork.com/</string>
	<key>ApptimizeAppKey</key>
	<string>CtXu3cPKXBdxgaPDXV6yfFXsbrqGuzQ</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
	<key>CFBundleShortVersionString</key>
	<string>11.6</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb233911114420181</string>
				<string>com.googleusercontent.apps.792238485434-rq28rtse24t3dnoiit6itss5rdgoeict</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>motorgy</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>Bunlde ID</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ebussiness-arabian-company.motorgy</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1497</string>
	<key>FacebooKWDisplayName</key>
	<string>motorgy</string>
	<key>FacebookAppID</key>
	<string>233911114420181</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fbapi20130214</string>
		<string>fbapi20130410</string>
		<string>fbapi20130702</string>
		<string>fbapi20131010</string>
		<string>fbapi20131219</string>
		<string>fbapi20140410</string>
		<string>fbapi20140116</string>
		<string>fbapi20150313</string>
		<string>fbapi20150629</string>
		<string>fbapi20160328</string>
		<string>fbshareextension</string>
		<string>tel</string>
		<string>fb</string>
		<string>twitter</string>
		<string>telprompt</string>
		<string>whatsapp</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>$(PRODUCT_NAME) needs Camera Access To That can allow you to update your ads photos or your profile</string>
	<key>NSCameraUsageDescription - 2</key>
	<string>$(PRODUCT_NAME) needs Camera Access To That can allow you to update your ads photos or your profile</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>$(PRODUCT_NAME) needs Location access to get your location to navigate you to company location</string>
	<key>NSLocationUsageDescription</key>
	<string>$(PRODUCT_NAME) needs Location access to get your location to navigate you to company location</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>$(PRODUCT_NAME) needs Location access to get your location to navigate you to company location</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>$(PRODUCT_NAME) needs to access the microphone to search cars using voice search function.

</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>$(PRODUCT_NAME) needs Photo Library Access That can allow you to update your ads photos or your profile</string>
	<key>NSPhotoLibraryUsageDescription - 2</key>
	<string>$(PRODUCT_NAME) needs Photo Library Access That can allow you to update your ads photos or your profile</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>$(PRODUCT_NAME) needs to access the microphone to search cars using voice search function.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>This will be used to serve a better experience and more relevant car ads to you.</string>
	<key>SKAdNetworkItems</key>
	<array>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>v9wttpbfk9.skadnetwork</string>
		</dict>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>n38lu8286q.skadnetwork</string>
		</dict>
	</array>
	<key>UIAppFonts</key>
	<array>
		<string>DIN Next Arabic Black.oft</string>
		<string>DIN Next Arabic Bold.otf</string>
		<string>DIN Next Arabic Heavy.otf</string>
		<string>DIN Next Arabic Light.otf</string>
		<string>DIN Next Arabic Medium.otf</string>
		<string>DINNextArabic-Regular.otf</string>
		<string>DIN Next Arabic Ultralight.otf</string>
		<string>DINNextLTArabic-Bold.ttf</string>
		<string>DINNextLTArabic-Light_1.ttf</string>
		<string>DINNextLTArabic-Medium.ttf</string>
		<string>DINNextLTArabic-Regular_1.ttf</string>
		<string>Cairo-Black.ttf</string>
		<string>Cairo-Bold.ttf</string>
		<string>Cairo-ExtraLight.ttf</string>
		<string>Cairo-Light.ttf</string>
		<string>Cairo-Regular.ttf</string>
		<string>Cairo-SemiBold.ttf</string>
	</array>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
					<key>UISceneStoryboardFile</key>
					<string>Main</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>UIApplicationShortcutItems</key>
	<array>
		<dict>
			<key>UIApplicationShortcutItemIconFile</key>
			<string>add-car</string>
			<key>UIApplicationShortcutItemSubtitle</key>
			<string>shortcutSubtitleSell</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>shortcutTitleSell</string>
			<key>UIApplicationShortcutItemType</key>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER).OpenSell</string>
		</dict>
		<dict>
			<key>UIApplicationShortcutItemIconFile</key>
			<string>search-icon</string>
			<key>UIApplicationShortcutItemSubtitle</key>
			<string>shortcutSubtitleSearch</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>shortcutTitleSearch</string>
			<key>UIApplicationShortcutItemType</key>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER).OpenSearch</string>
		</dict>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleLightContent</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
</dict>
</plist>
