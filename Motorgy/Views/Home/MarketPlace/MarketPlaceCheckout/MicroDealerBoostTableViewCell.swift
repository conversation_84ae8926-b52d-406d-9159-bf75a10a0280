//
//  MicroDealerBoostTableViewCell.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 14/07/2025.
//  Copyright © 2025 <PERSON><PERSON>. All rights reserved.
//


import SwiftUI

class MicroDealerBoostTableViewCell: UITableViewCell, CellReusableIdentifier {
    private var hostingController: UIHostingController<BoostView>?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupCell()
    }
    
    private func setupCell() {
        backgroundColor = .clear
        selectionStyle = .none
        separatorInset = UIEdgeInsets(top: 0, left: 24, bottom: 0, right: 24)
    }
    
    func configure(with selectedBoostPackage: PackageBoostModel?, editBoostAction: @escaping (() -> Void), isRenewBundle: Bool) {
        hostingController?.view.removeFromSuperview()
        hostingController?.removeFromParent()

        let swiftUIView = BoostView(selectedBoostPackage: selectedBoostPackage, editBoostAction: editBoostAction, isRenewBundle: isRenewBundle)
        let hostingController = UIHostingController(rootView: swiftUIView)
        hostingController.view.backgroundColor = .clear
        self.hostingController = hostingController
        
        contentView.addSubview(hostingController.view)
        hostingController.view.translatesAutoresizingMaskIntoConstraints = false
        hostingController.view.semanticContentAttribute = !LanguageHelper.isEnglish ? .forceRightToLeft : .forceLeftToRight
        
        NSLayoutConstraint.activate([
            hostingController.view.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            hostingController.view.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            hostingController.view.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            hostingController.view.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -12)
        ])
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        hostingController?.view.removeFromSuperview()
        hostingController?.removeFromParent()
        hostingController = nil
    }
}

struct BoostView: View {
    let selectedBoostPackage: PackageBoostModel?
    let editBoostAction: () -> Void
	let isRenewBundle: Bool
    
    private struct CurrencyText: View {
        let amount: String
        var body: some View {
            HStack(spacing: 4) {
                if LanguageHelper.isEnglish {
                    Text(amount)
                    Text("KWD")
                } else {
                    Text("د.ك")
                    Text(amount)
                }
            }
            .environment(\.layoutDirection, .leftToRight)
        }
    }

    var body: some View {
        if let selectedBoostPackage = selectedBoostPackage {
            // Show selected boost package
            ZStack(alignment: .topLeading) {
                VStack(spacing: 16) {
                    HStack(alignment: .top) {
                        featuresList
                            .padding(.bottom, 16)
                            .padding(.top, 8)

                        HStack(spacing: 8) {
                            CurrencyText(amount: selectedBoostPackage.Price?.formattedWithWithoutFraction() ?? "")
                                .font(
                                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                        .weight(.bold)
                                )
                                .foregroundColor(.black)

                            if self.isRenewBundle == false {
                                Button(action: editBoostAction) {
                                    Image("edit_sell_car")
                                        .resizable()
                                        .frame(width: 32, height: 32)
                                }
                            }
                        }
                    }
                    .padding(.top, 16)
                }
                .padding(.horizontal, 20)
                .background(Color.white)
                .cornerRadius(12)

                .shadow(color: Color(red: 0.95, green: 0.96, blue: 0.97), radius: 0, x: 0, y: 4)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .inset(by: 0.5)
                        .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), lineWidth: 1)
                )

                Text(selectedBoostPackage.Title ?? "")
                    .font(
                        Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                            .weight(.semibold)
                    )
                    .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                    .padding(.horizontal, 10)
                    .padding(.vertical, 6)
                    .background(Color.white)
                    .cornerRadius(100)
                    .overlay(
                        RoundedRectangle(cornerRadius: 100)
                            .inset(by: 0.5)
                            .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), lineWidth: 1)
                    )
                    .padding(.leading, 20)
                    .offset(y: -14)
            }
        } else {
            // Show "Add Boost Package" option
            Button(action: editBoostAction) {
                HStack {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Add Boost Package".localized)
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 16)
                                    .weight(.semibold)
                            )
                            .foregroundColor(.black)

                        Text("Boost your listing visibility".localized)
                            .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14))
                            .foregroundColor(.gray)
                    }

                    Spacer()

                    Image(systemName: "plus.circle")
                        .font(.title2)
                        .foregroundColor(Color(uiColor: Colors.bluishColor))
                }
                .padding(20)
                .background(Color.white)
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .inset(by: 0.5)
                        .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), lineWidth: 1)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    private var featuresList: some View {
        VStack(alignment: .leading, spacing: 12) {
            ForEach(selectedBoostPackage?.Boosters ?? [], id: \.Description) { feature in
                HStack(spacing: 12) {
                    boldedNumberText(text: feature.Description ?? "")
                        .font(Font.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14))
                        .foregroundColor(.black)

                    Spacer()
                }
            }
        }
    }
}
