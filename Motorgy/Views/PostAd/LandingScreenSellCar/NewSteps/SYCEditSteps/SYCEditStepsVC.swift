//
//  SYCEditStepsVC.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 04/10/2023.
//  Copyright © 2023 <PERSON><PERSON>. All rights reserved.
//

import UIKit

class SYCEditStepsVC: BaseVC {
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var customButtonView: SellingProcessCustomButtonView!
    
    private var userData: [String: Any] = [:]
    private var isTrim = false
    private let carDetailsViewModel = MyCarDetailsViewModel()
    private var cachedPackagesData: [LstPackages] = []
    private var newPackageData: [LstPackages] = []
    private var adIdToPostApi = 0
    private var userSelectedNewData: [Int: Any] = [:]
    private var selectedPakcage: LstPackages?
    private var uploadedImages: [UploadImageItemModel]?
    
    override func viewDidLoad() {
        super.viewDidLoad()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        isFirstMileageTimeLoad = false
        isFirstPriceTimeLoad = false
        
        self.title = "Car details".localized
        
        self.setupTableView()
        
        self.view.backgroundColor = UIColor.hexStringToUIColor(hex: "#F2F4F7")
        
        self.customButtonView.configureConfirmEditing {
            let modelId = (self.userData["1"] as? DataAttribute)?.id ?? 0
            let year = (self.userData["\(self.isTrim ? 3 : 2)"] as? DataAttribute)?.id  ?? 0
            let mileage = (self.userData["\(self.isTrim ? 4 : 3)"] as? DataAttribute)?.id  ?? 0
            
            let isSelfSellingCar = SelfServiceDataSource.shared.getIsSelectedRouteSelected()
            
			self.carDetailsViewModel.getPackageDetails(modelId: modelId, year: year, mileage: mileage, isSelfSellingCar: isSelfSellingCar, adId: nil, isFromUpgrade: nil, estimatedPrice: nil).bind { [weak self] packages in
				DispatchQueue.main.async {
					if self?.cachedPackagesData == packages {
						self?.goToCheckoutScreen()
					} else {
						self?.newPackageData = packages ?? []
						self?.presentPackagesUpdatedActionSheet()
					}
				}
			}.disposed(by: self.carDetailsViewModel.getDisposeBag())
        }
    }
    
    private func goToCheckoutScreen() {
        if let navigationController = self.navigationController {
            for vc in navigationController.viewControllers.reversed() {
                if let checkoutViewController = vc as? SYCProcessCheckoutVC {
                    checkoutViewController.updateUserData(modifiedUserData: self.userData)
                    navigationController.popToViewController(checkoutViewController, animated: true)
                    break
                }
            }
        }
    }
    
    private func presentPackagesUpdatedActionSheet() {
        let timeSlotNotAvailableVC = self.getNextViewController(viewControllerClass: SYCSlotNotAvailableSheetVC.self, storyBoardName: "SellYouCar", identifier: "SYCSlotNotAvailableSheetVC") ?? SYCSlotNotAvailableSheetVC()
        timeSlotNotAvailableVC.modalPresentationStyle = .custom
        timeSlotNotAvailableVC.setData(controller: self, screenType: .packageUpdated)
        timeSlotNotAvailableVC.packagesDelegate = self
        self.present(timeSlotNotAvailableVC, animated: true)
    }
    
    public func configureWith(userData: [String: Any], isTrim: Bool, allPakcages: [LstPackages]? = nil, adIdToPostApi: Int? = nil, userSelectedNewData: [Int: Any], selectedPakcage: LstPackages?) {
        self.userData = userData
        self.isTrim = isTrim
        self.cachedPackagesData = allPakcages ?? []
        self.adIdToPostApi = adIdToPostApi ?? 0
        self.userSelectedNewData = userSelectedNewData
        self.selectedPakcage = selectedPakcage
    }
    
    public func configureEditImages(uploadedImages: [UploadImageItemModel]?) {
        self.uploadedImages = uploadedImages
    }
    
    private func setupTableView() {
        self.view.backgroundColor = UIColor.hexStringToUIColor(hex: "#F2F4F7")
        self.tableView.delegate = self
        self.tableView.dataSource = self
        self.tableView.register(SYCEditDetailsTVC.nib(), forCellReuseIdentifier: SYCEditDetailsTVC.identifier)
        self.tableView.estimatedRowHeight = UITableView.automaticDimension
        self.tableView.separatorStyle = .none
        self.tableView.showsVerticalScrollIndicator = false
        self.tableView.isScrollEnabled = false
        self.tableView.reloadData()
    }
}

extension SYCEditStepsVC: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.isTrim ? 6 : 5
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: SYCEditDetailsTVC.identifier, for: indexPath) as? SYCEditDetailsTVC
        cell?.selectionStyle = .none
        
        let mileageIndexPath = self.isTrim ? 4 : 3
        let priceIndexPath = self.isTrim ? 5 : 4
        let attribute = self.userData["\(indexPath.row)"]as? DataAttribute
        
        if indexPath.row == priceIndexPath {
            cell?.configureCell(key: attribute?.key ?? "", value: "\((attribute?.id ?? 0).withCommas()) " + "KWD".localized, type: .normalEdit)
        } else if indexPath.row == mileageIndexPath {
            cell?.configureCell(key: attribute?.key ?? "", value: (attribute?.name ?? "") == "Mileage" ? "0 " + "KM".localized : (attribute?.name ?? ""), type: .normalEdit)
        } else {
            cell?.configureCell(key: attribute?.key ?? "", value: attribute?.name ?? "", type: .normalEdit)
        }
        
        return cell ?? UITableViewCell()
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        var type: String? , data: [Any]?
        
        switch indexPath.row {
            case 0:
                type = BrandModelResult.shared.self?.type
                data = BrandModelResult.shared.self?.lstBrandModelYear
                
            case 1:
                let brandId = (self.userData["0"] as? DataAttribute)?.id
                let brand = BrandModelResult.shared.self?.lstBrandModelYear?.filter({$0.brandID == brandId})
                type = brand?[0].lstModels?[0].type
                data = brand?[0].lstModels
                
            case 2:
                let brandId = (self.userData["0"] as? DataAttribute)?.id
                let modelId = (self.userData["1"] as? DataAttribute)?.id
                let brand = BrandModelResult.shared.self?.lstBrandModelYear?.filter({$0.brandID == brandId})
                let model = brand?[0].lstModels?.filter({$0.modelID == modelId})
                
                if (model?[0].lstTrims?.count ?? 0) > 1 {
                    type = "Trim".localized
                    data = model?[0].lstTrims
                } else {
                    type = model?[0].nextType
                    data = model?[0].lstYear
                }
                
            case self.isTrim ? 3 : 2:
                let brandId = (self.userData["0"] as? DataAttribute)?.id
                let brand = BrandModelResult.shared.self?.lstBrandModelYear?.filter({$0.brandID == brandId})
                let modelId = (self.userData["1"] as? DataAttribute)?.id
                let model = brand?[0].lstModels?.filter({$0.modelID == modelId})
                type = model?[0].nextType
                data = model?[0].lstYear
                
            case self.isTrim ? 4 : 3:
                type = BrandModelResult.shared.self?.lstMileage?[0].type
                data = BrandModelResult.shared.self?.lstMileage
                
            default:
                break
        }
        
        if indexPath.row == (self.isTrim ? 4 : 3) {
            self.movetoKM(isFirst: true, type: type, data: data, isEdit: true, canEdit: true, isTrim: self.isTrim)
        } else if indexPath.row == (self.isTrim ? 5 : 4) {
            self.movetoPriceRange(isFirst: true, type: type, data: data, isEdit: true, canEdit: true, isTrim: self.isTrim)
        } else {
            self.moveToCarDetails(isFirst: true, type: type, data: data, isEdit: true, canEdit: true, isTrim: self.isTrim)
        }
    }
}

extension SYCEditStepsVC {
    private func moveToCarDetails(isFirst: Bool, type: String?, data: [Any]?, isEdit: Bool, canEdit: Bool, isTrim: Bool) {
        let carDetailsVC = self.getNextViewController(viewControllerClass: CarDetailsVC.self, storyBoardName: "SellYouCar", identifier: "CarDetailsVC") ?? CarDetailsVC()
        carDetailsVC.setData(
            brandModelResult: BrandModelResult.shared.self,
            onDismiss: nil,
            isFirst: isFirst,
            type: type,
            data: data,
            userData: userData,
            serviceId: 0,
            isEdit: isEdit,
            canEdit: canEdit,
            isTrim: isTrim
        )
        carDetailsVC.setPackagesData(allPackages: self.cachedPackagesData)
        carDetailsVC.setDataForSelfSelling(userSelectedNewData: nil, selectedPakcage: self.selectedPakcage)
        carDetailsVC.setHideHorizontalTagsListWhenOpenFromEditNormalPackagesScreenFromCheckout(hideTagsListFromNormalEditAfterCheckout: true)
        self.navigationController?.pushViewController(carDetailsVC, animated: true)
    }
    
    private func movetoKM (isFirst: Bool, type: String?, data: [Any]?, isEdit: Bool, canEdit: Bool, isTrim: Bool) {
        let kmVC = self.getNextViewController(viewControllerClass: KMVC.self, storyBoardName: "SellYouCar", identifier: "KMVC") ?? KMVC()
        kmVC.navigationController?.navigationBar.topItem?.title = ""
        kmVC.setupData(
            onDismiss: nil,
            isFirst: isFirst,
            type: type,
            data: data,
            brandModelResult: BrandModelResult.shared.self,
            userData: userData,
            isEdit: isEdit,
            canEdit: canEdit,
            isTrim: isTrim
        )
        kmVC.setInspectionAndPackagesData(lstPackages: self.cachedPackagesData, lstInspectionLocations: nil, lstCityWithAreas: nil, lstMasterPackages: nil)
        kmVC.setDataForSelfSelling(userSelectedNewData: nil, selectedPakcage: self.selectedPakcage)
        kmVC.setHideHorizontalTagsListWhenOpenFromEditNormalPackagesScreenFromCheckout(hideTagsListFromNormalEditAfterCheckout: true)
        self.navigationController?.pushViewController(kmVC, animated: true)
    }
    
    private func movetoPriceRange (isFirst: Bool, type: String?, data: [Any]?, isEdit: Bool, canEdit: Bool, isTrim: Bool) {
        let priceRangeVC = self.getNextViewController(viewControllerClass: PriceRangeVC.self, storyBoardName: "SellYouCar", identifier: "PriceRangeVC") ?? PriceRangeVC()
        priceRangeVC.navigationController?.navigationBar.topItem?.title = ""
        priceRangeVC.setupData(
            onDismiss: nil,
            isFirst: isFirst,
            type: type,
            data: data,
            brandModelResult: BrandModelResult.shared.self,
            userData: userData,
            isEdit: isEdit,
            canEdit: canEdit,
            isTrim: isTrim
        )
        priceRangeVC.setInspectionAndPackagesData(lstPackages: self.cachedPackagesData, lstInspectionLocations: nil, lstCityWithAreas: nil, lstMasterPackages: nil)
        priceRangeVC.setDataForSelfSelling(userSelectedNewData: nil, selectedPakcage: self.selectedPakcage)
        priceRangeVC.setHideHorizontalTagsListWhenOpenFromEditNormalPackagesScreenFromCheckout(hideTagsListFromNormalEditAfterCheckout: true)
        self.navigationController?.pushViewController(priceRangeVC, animated: true)
    }
}

extension SYCEditStepsVC: SYCChooseAnotherPackageSheetVCProtocol {
    func chooseAnotherPackage() {
        if let navigationController = self.navigationController {
            for vc in navigationController.viewControllers {
                if let packagesViewController = vc as? SYCProcessPackagesVC {
                    packagesViewController.updateUserData(modifiedUserData: self.userData, adIdToPostApi: self.adIdToPostApi)
                    packagesViewController.setLstPackagesUpdatedFromEditDetailsScreen(newPackagesData: self.newPackageData, alreadyCalledApi: true)
                    navigationController.popToViewController(packagesViewController, animated: true)
                    break
                }
            }
        }
    }
}
