//
//  SellingMultipleRouter.swift
//  Motorgy
//
//  Created by <PERSON> on 03/06/2025.
//  Copyright © 2025 <PERSON><PERSON>. All rights reserved.
//

import UIKit
import SwiftUI

struct SellingMultipleRouter {
    static func build(from navigationController: UINavigationController) {
        let viewModel = MicroDealerViewModel(navigationController: navigationController)
        let view = SellingMultipleCarsScreenView(viewModel: viewModel)
        let host = HostingController(rootView: view, hideNavigationBar: true)
        navigationController.pushViewController(host, animated: true)
    }
    
    static func moveToBoostVisibility(from navigationController: UINavigationController, viewModel: MicroDealerViewModel) {
        let view = BoostVisibilityView(viewModel: viewModel)
        let host = HostingController(rootView: view, hideNavigationBar: true)
        navigationController.pushViewController(host, animated: true)
    }
    
    static func moveToBuyBundleForM<PERSON>roDealerCheckout(from navigationController: UINavigationController, viewModel: MicroDealerViewModel) {
        if let marketPlaceCheckoutVC = UIStoryboard.init(name: "MarketPlace", bundle: nil).instantiateViewController(withIdentifier: "MarketPlaceCheckoutVC") as? MarketPlaceCheckoutVC {
            marketPlaceCheckoutVC.setOpenFromMicroDealerBuyBundleFlow(isOpenFromMicroDealerBuyBundleFlow: true, microDealerViewModel: viewModel)

            // Hide back button text on the checkout screen
            marketPlaceCheckoutVC.navigationItem.backButtonTitle = ""

            // Set the current (previous) view controller's back bar button item to hide text
            if let currentVC = navigationController.topViewController {
                currentVC.navigationItem.backBarButtonItem = UIBarButtonItem(title: "", style: .plain, target: nil, action: nil)
            }

            // Ensure navigation bar is properly configured before pushing
            navigationController.setNavigationBarHidden(false, animated: false)

            navigationController.pushViewController(marketPlaceCheckoutVC, animated: true)
        }
    }
	
	static func moveToMicroDealerCheckoutForRenewBundle(from navigationController: UINavigationController, viewModel: MicroDealerViewModel?, isRenewBundle: Bool) {
		if let marketPlaceCheckoutVC = UIStoryboard.init(name: "MarketPlace", bundle: nil).instantiateViewController(withIdentifier: "MarketPlaceCheckoutVC") as? MarketPlaceCheckoutVC {

			// Create viewModel if it's nil
			let vm = viewModel ?? MicroDealerViewModel(navigationController: navigationController)

			marketPlaceCheckoutVC.setOpenFromMicroDealerBuyBundleFlow(isOpenFromMicroDealerBuyBundleFlow: true, microDealerViewModel: vm)
			marketPlaceCheckoutVC.setOpenFromMicroDealerForRenewBundle(isRenewMicroDelaerBundle: isRenewBundle)

			marketPlaceCheckoutVC.navigationItem.backButtonTitle = ""

			if let currentVC = navigationController.topViewController {
				currentVC.navigationItem.backBarButtonItem = UIBarButtonItem(title: "", style: .plain, target: nil, action: nil)
			}

			navigationController.setNavigationBarHidden(false, animated: false)

			navigationController.pushViewController(marketPlaceCheckoutVC, animated: true)
		}
	}
}
