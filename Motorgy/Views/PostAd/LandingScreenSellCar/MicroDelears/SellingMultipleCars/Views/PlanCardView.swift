//
//  PlanCardView.swift
//  Motorgy
//
//  Created by <PERSON> on 03/06/2025.
//  Copyright © 2025 <PERSON><PERSON>. All rights reserved.
//

import SwiftUI

struct PlanCardView: View {
    let isSelected: Bool
    let model: PackageBundelModel
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            ZStack {
                VStack(spacing: 0) {
                    HStack {
                        Circle()
                            .stroke(isSelected ? Color(uiColor: Colors.bluishColor) : Color.gray.opacity(0.4), lineWidth: 1)
                            .frame(width: 20, height: 20)
                            .overlay(
                                Circle()
                                    .fill(Color(uiColor: Colors.bluishColor))
                                    .frame(width: 10, height: 10)
                                    .opacity(isSelected ? 1 : 0)
                            )
                        
                        Text(model.packageName ?? "")
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                    .weight(.semibold)
                            )
                            .foregroundColor(.black)

                        Spacer()
                        
                        HStack(spacing: 6) {
							VSta<PERSON>(spacing: 0) {
								HStack(spacing: 2) {
									Text("\(model.bundleCarPrice?.formattedWithWithoutFraction() ?? "") \("KWD".localized)")
										.font(
											Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
												.weight(.semibold)
										)
										.foregroundColor(Color.init(uiColor: Colors.slateColor))
									
									Text(LanguageHelper.isEnglish ? "/car" : "/سيارة")
										.font(
											Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
												.weight(.regular)
										)
										.foregroundColor(Color.init(uiColor: Colors.slateColor))
								}
								.overlay(alignment: .bottom) {
									Rectangle()
										.stroke(
											Color(
												uiColor: UIColor.hexStringToUIColor(hex: "#FDB022")
											),
											style: StrokeStyle(lineWidth: 1, dash: [2, 2])
										)
										.frame(height: 1)
								}
							}
							
							Text("\(model.price?.formattedWithWithoutFraction() ?? "") \("KWD".localized)")
								.font(
									Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
										.weight(.bold)
								)
								.foregroundColor(.black)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 24)
                    .padding(.bottom, 10)
                    
                    DashedDivider()
                        .padding(.horizontal, 20)
                        .padding(.top, 8)
                        .padding(.bottom, 8)

                    VStack(spacing: 4) {
                        ForEach(model.packageFeatures ?? [], id: \.featureId) { packageFeature in
                            FeatureRow(iconUrl: packageFeature.featureIcon ?? "", text: packageFeature.featureName ?? "")
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
                }
                .background(Color.white)
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isSelected ? Color(uiColor: Colors.bluishColor) : Color.clear, lineWidth: 1)
                )
                
                if model.bundleDiscountPercentage ?? 0 > 0 {
                    HStack {
                        Spacer()
                        
                        Text("\(model.bundleDiscountPercentage?.formattedWithWithoutFraction() ?? "")% \(LanguageHelper.isEnglish ? "OFF" : "خصم")")
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 10)
                                    .weight(.semibold)
                            )
                            .foregroundColor(Color(red: 1, green: 0.27, blue: 0.23))
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color(red: 1, green: 0.93, blue: 0.92))
                            .cornerRadius(4)
                            .offset(x: -12, y: -8)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topTrailing)
                }
            }
        }
        .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)
        .buttonStyle(PlainButtonStyle())
    }
}
