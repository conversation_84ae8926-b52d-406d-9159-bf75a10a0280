//
//  SellingMultipleCarsScreenView.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 30/05/2025.
//  Copyright © 2025 <PERSON><PERSON>. All rights reserved.
//

import SwiftUI

struct SellingMultipleCarsScreenView: View {
    @ObservedObject var viewModel: MicroDealerViewModel

    var body: some View {
        NavigationView {
            ZStack {
                Color(.systemGray6).ignoresSafeArea()

                VStack(spacing: 0) {
                    ScrollView {
                        VStack(spacing: 0) {
//                            if self.viewModel.isApiLoaded {
//                                NotificationBanner {
//                                    viewModel.getContact()
//                                }
//                            }
                            
                            PlansSection(
                                selectedPackageBundle: $viewModel.selectedPackageBundle,
                                packageBundles: viewModel.packageBundles
                            )
                            .padding(.bottom, 32)
                            .padding(.top, 24)
							
							if ConstantsValues.sharedInstance.isOpenBoostVisibilityMicroDealerScreenFromPackages {
								VStack(alignment: .center, spacing: 12) {
									Text(LanguageHelper.isEnglish ? "Just one car to sell?" : "هل ستبيع أكثر من سيارة؟")
										.font(
											Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
												.weight(.semibold)
										)
										.foregroundStyle(.black)
									
									HStack {
										Image(systemName: LanguageHelper.isEnglish ? "chevron.left" : "chevron.right")
											.foregroundStyle(Color(red: 0, green: 0.47, blue: 1))
										
										Text(LanguageHelper.isEnglish ? "View single-seller deals" : "شاهد باقات الخصم لدينا!")
											.font(
												Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
													.weight(.semibold)
											)
											.foregroundStyle(Color(red: 0, green: 0.47, blue: 1))
									}
									.padding(.horizontal, 24)
									.padding(.vertical, 6)
									.foregroundColor(.clear)
									.cornerRadius(100)
									.overlay(
										RoundedRectangle(cornerRadius: 100)
											.inset(by: 0.25)
											.stroke(Color(red: 0, green: 0.47, blue: 1), lineWidth: 1.0)
									)
								}
								.padding(.bottom, 24)
								.onTapGesture {
									viewModel.buySingleCar()
								}
							}
                            
                            if self.viewModel.isApiLoaded {
                                HelpSection {
                                    viewModel.getHelp()
                                }
                            }
                        }
                    }
                    
                    if self.viewModel.isApiLoaded {
                        StickyContinueButton {
                            viewModel.onContinueBundle()
                        }
                    }
                }
            }
            .navigationTitle(LanguageHelper.isEnglish ? "Sell multiple cars" : "بيع عدة سيارات")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {                    
                    Button {
                        viewModel.goBack()
                    } label: {
                        Image(systemName: LanguageHelper.isEnglish ? "chevron.left" : "chevron.right")
                            .foregroundStyle(.black)
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel".localized, action: viewModel.cancelCompletely)
                        .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14))
                        .foregroundStyle(Color(uiColor: Colors.bluishColor))
                }
            }
            .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
            .onAppear {
                self.viewModel.getPackageBundlesDetails(isBundle: true, includeSelfService: true)
            }
        }
    }
}
