//
//  BundleSelectionDialog.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 20/07/2025.
//  Copyright Bo<PERSON>. All rights reserved.

import SwiftUI

// MARK: - Bundle Selection Bottom Sheet
struct BundleSelectionDialog: View {
    @Binding var isPresented: Bool
    @State private var selectedBundleId: Int
    
    let bundles: [BundleModelMyCars]
    let onBundleSelected: (BundleModelMyCars) -> Void
    let onCancel: () -> Void
    let onDismiss: () -> Void
    
    init(
        isPresented: Binding<Bool>,
        bundles: [BundleModelMyCars],
        selectedBundleId: Int? = nil,
        onBundleSelected: @escaping (BundleModelMyCars) -> Void,
        onCancel: @escaping () -> Void = {},
        onDismiss: @escaping () -> Void = {}
    ) {
        self._isPresented = isPresented
        self.bundles = bundles
        self._selectedBundleId = State(initialValue: selectedBundleId ?? bundles.first?.id ?? 0)
        self.onBundleSelected = onBundleSelected
        self.onCancel = onCancel
        self.onDismiss = onDismiss
    }
    
    var selectedBundle: BundleModelMyCars? {
        bundles.first { $0.id == selectedBundleId }
    }
    
    var body: some View {
        ZStack {
            Color.black.opacity(0.3)
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Header section with handle and title
                VStack(alignment: .leading, spacing: 0) {
                    HStack {
                        Text(LanguageHelper.isEnglish ? "Choose bundle to proceed" : "أختر باقة للمتابعة")
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 16)
                                    .weight(.bold)
                            )
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        Button {
                            onDismiss()
                            ConstantsValues.sharedInstance.isListingCarInMicroDealerFlowStarted = false
                            ConstantsValues.sharedInstance.activeSubscriptionMicroDealer = nil
                        } label: {
                            ZStack {
                                Circle()
                                    .fill(Color.gray.opacity(0.2))
                                    .frame(width: 32, height: 32)
                                Image(systemName: "xmark")
                                    .foregroundColor(.gray)
                                    .font(.system(size: 14, weight: .medium))
                            }
                        }
                    }
                    .padding(.horizontal, 24)
                    .padding(.bottom, 24)
                    .padding(.top, 16)
                    
                    DashedDivider()
                        .padding(.horizontal, 24)
                        .padding(.bottom, 24)
                }
                .background(Color.white)
                
                // Scrollable content section - only use ScrollView when needed
                if shouldUseScrollView() {
                    ScrollView {
                        VStack(spacing: 8) {
                            ForEach(bundles, id: \.id) { bundle in
                                BundleCard(
                                    bundle: bundle,
                                    isSelected: bundle.id == selectedBundleId,
                                    onBundleSelected: { selectedBundle in
                                        selectedBundleId = selectedBundle.id ?? 0
                                    }
                                )
                            }
                        }
                        .padding(.horizontal, 24)
                        .padding(.bottom, 16)
                    }
                    .background(Color.white)
                    .frame(maxHeight: calculateMaxScrollHeight())
                } else {
                    // Direct VStack for small content - no ScrollView needed
                    VStack(spacing: 8) {
                        ForEach(bundles, id: \.id) { bundle in
                            BundleCard(
                                bundle: bundle,
                                isSelected: bundle.id == selectedBundleId,
                                onBundleSelected: { selectedBundle in
                                    selectedBundleId = selectedBundle.id ?? 0
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 24)
                    .padding(.bottom, 16)
                    .background(Color.white)
                }
                
                // Action buttons at bottom with top shadow
                HStack(spacing: 12) {
                    Button {
                        onCancel()
                        dismissSheet()
                        ConstantsValues.sharedInstance.isListingCarInMicroDealerFlowStarted = false
                        ConstantsValues.sharedInstance.activeSubscriptionMicroDealer = nil
                    } label: {
                        Text("Cancel".localized)
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 16)
                                    .weight(.bold)
                            )
                            .foregroundColor(.gray)
                            .frame(maxWidth: .infinity)
                            .frame(height: 40)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                    }
                    
                    Button {
                        if let selected = bundles.first(where: { $0.id == selectedBundleId }) {
                            onBundleSelected(selected)
                            dismissSheet()
                        }
                    } label: {
                        Text("Continue".localized)
                            .font(
                                Font.custom("Inter", size: 16)
                                    .weight(.bold)
                            )
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 40)
                            .background(Color.green)
                            .cornerRadius(8)
                    }
                }
                .padding(.horizontal, 24)
                .padding(.top, 16)
                .padding(.bottom, getSafeAreaBottom())
                .background(Color.white)
                .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: -2)
            }
            .frame(maxWidth: .infinity)
            .cornerRadius(16, corners: [.topLeft, .topRight])
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottom)
            .ignoresSafeArea(.all, edges: .bottom)
        }
        .animation(.spring(response: 0.5, dampingFraction: 0.8, blendDuration: 0), value: isPresented)
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    // Determine if ScrollView is needed based on content size
    private func shouldUseScrollView() -> Bool {
        let estimatedContentHeight = calculateEstimatedContentHeight()
        let maxAllowedHeight = UIScreen.main.bounds.height * 0.6
        let fixedElementsHeight = calculateFixedElementsHeight()
        
        return (estimatedContentHeight + fixedElementsHeight) > maxAllowedHeight
    }
    
    // Calculate estimated height of bundle cards
    private func calculateEstimatedContentHeight() -> CGFloat {
        let estimatedCardHeight: CGFloat = 80 // Approximate height of each BundleCard
        let cardSpacing: CGFloat = 8
        let horizontalPadding: CGFloat = 48 // 24 on each side
        let bottomPadding: CGFloat = 16
        
        let totalCardsHeight = CGFloat(bundles.count) * estimatedCardHeight
        let totalSpacing = CGFloat(max(0, bundles.count - 1)) * cardSpacing
        
        return totalCardsHeight + totalSpacing + horizontalPadding + bottomPadding
    }
    
    // Calculate height of fixed elements (header + buttons)
    private func calculateFixedElementsHeight() -> CGFloat {
        let headerHeight: CGFloat = 12 + 8 + 18 + 24 + 24 // handle + spacing + title + padding
        let buttonHeight: CGFloat = 16 + 40 + getSafeAreaBottom() // padding + button + safe area
        let shadowSpace: CGFloat = 8 // space for shadow
        
        return headerHeight + buttonHeight + shadowSpace
    }
    
    // Calculate maximum height for scroll view based on available space
    private func calculateMaxScrollHeight() -> CGFloat {
        let screenHeight = UIScreen.main.bounds.height
        let maxSheetHeight = screenHeight * 0.6
        
        // Fixed heights for header and buttons
        let headerHeight: CGFloat = 12 + 8 + 18 + 24 + 24 // handle + spacing + title + padding
        let buttonHeight: CGFloat = 16 + 40 + getSafeAreaBottom() // padding + button + safe area
        let shadowSpace: CGFloat = 8 // space for shadow
        
        // Available height for scroll content
        let availableHeight = maxSheetHeight - headerHeight - buttonHeight - shadowSpace
        
        // Minimum height to show at least one item properly
        let minimumHeight: CGFloat = 100
        
        return max(minimumHeight, availableHeight)
    }
    
    private func dismissSheet() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8, blendDuration: 0)) {
            isPresented = false
        }
    }
    
    private func getSafeAreaBottom() -> CGFloat {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return 16
        }
        return max(16, window.safeAreaInsets.bottom)
    }
}

struct BundleCard: View {
    let bundle: BundleModelMyCars
    let isSelected: Bool
    let onBundleSelected: (BundleModelMyCars) -> Void
    
    private var usageIndicator: some View {
        HStack(spacing: 0) {
            Text("\(bundle.numberOfUsedAds ?? 0)")
                .font(
                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                        .weight(.bold)
                )
                .padding(.leading, 5)
                .padding(.vertical, 2)
            Text(LanguageHelper.isEnglish ? "/\(bundle.numberOfAds ?? 0)" : "\(bundle.numberOfAds ?? 0)/")
                .font(
                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                        .weight(.regular)
                )
                .padding(.trailing, 5)
                .padding(.vertical, 2)
        }
        .foregroundColor(.black)
        .background(Color.init(uiColor: UIColor.hexStringToUIColor(hex: "#F2F4F7")))
        .clipShape(Capsule())
    }
    
    var body: some View {
        HStack(alignment: .top) {
            VStack(alignment: .leading) {
                HStack(spacing: 0) {
                    Text(bundle.packageName ?? "")
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                .weight(.bold)
                        )
                        .foregroundColor(.black)
                    
                    if let boostName = bundle.boostPackageName, !boostName.isEmpty, boostName != "<null>" {
                        Text(" + ")
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                    .weight(.semibold)
                            )
                            .foregroundColor(.black)
                        
                        Text(boostName)
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                    .weight(.semibold)
                            )
                            .foregroundColor(.black)
                            .background(alignment: .bottom) {
                                Color.yellow
                                    .opacity(0.4)
                                    .frame(height: 4)
                            }
                    }
                }
                
                HStack(spacing: 4) {
                    usageIndicator
                    
                    Text("\(LanguageHelper.isEnglish ? "Valid until \(convertDateManually(bundle.expirationDate ?? "", showWithoutTime: true))" : "متاحة حتي \(convertDateManually(bundle.expirationDate ?? "", showWithoutTime: true))")")
                        .font(Font.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 12))
                        .foregroundColor(Color.init(uiColor: Colors.slateColor))
                }
            }
            
            Spacer()
            
            Circle()
                .stroke(isSelected ? Color(uiColor: Colors.bluishColor) : Color.gray.opacity(0.4), lineWidth: 1)
                .frame(width: 20, height: 20)
                .overlay(
                    Circle()
                        .fill(Color(uiColor: Colors.bluishColor))
                        .frame(width: 15, height: 15)
                        .opacity(isSelected ? 1 : 0)
                )
        }
        .padding(16)
        .background(.white)
        .cornerRadius(12)
        .shadow(color: Color(red: 0.95, green: 0.96, blue: 0.97), radius: 0, x: 0, y: 4)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .inset(by: 0.5)
                .stroke(Color(red: 0.95, green: 0.96, blue: 0.97), lineWidth: 1)
        )
        .padding(.bottom, 16)
        .onTapGesture {
            onBundleSelected(bundle)
        }
    }
}

// MARK: - Custom Corner Radius Extension
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}
