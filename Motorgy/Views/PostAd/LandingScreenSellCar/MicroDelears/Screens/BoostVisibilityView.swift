//
//  BoostVisibilityView.swift
//  Motorgy
//
//  Created by <PERSON> on 03/06/2025.
//  Copyright 2025 <PERSON><PERSON>. All rights reserved.
//

import SwiftUI
import Kingfisher

struct BoostVisibilityView: View {
    @ObservedObject var viewModel: MicroDealerViewModel
    @State private var summaryViewHeight: CGFloat = 0

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                header
                
                ZStack(alignment: .top) {
                    ScrollView {
                        VStack(spacing: 0) {
                            VStack(spacing: 24) {
                                IntroSectionView()
                                
                                BoostOptionsView(viewModel: viewModel)
                                    .padding(.horizontal, 20)
                                
                                InfoNoteView()
                                    .padding(.horizontal, 20)
                            }
                            .padding(.top, summaryViewHeight)
                        }
                    }
                    
//                    SelectedPlanSummaryView(viewModel: viewModel)
//                        .background(
//                            GeometryReader { geometry in
//                                Color.clear
//                                    .preference(key: HeightPreferenceKey.self, value: geometry.size.height)
//                            }
//                        )
                }
                .onPreferenceChange(HeightPreferenceKey.self) { newHeight in
                    self.summaryViewHeight = newHeight
                }
                
                BottomSectionView(viewModel: viewModel)
            }
            .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
            .onAppear {
                self.viewModel.getBoostPackages()
            }
        }
    }
    
    var header: some View {
        ZStack {
            HStack {
                Button {
                    viewModel.goBack()
                } label: {
                    Image(systemName: LanguageHelper.isEnglish ? "chevron.left" : "chevron.right")
                        .foregroundStyle(.black)
                }
                
                Spacer()
                
                Button {
                    viewModel.cancelCompletely()
                } label: {
                    Text("Cancel".localized)
                        .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14))
                        .foregroundColor(Color(uiColor: Colors.bluishColor))
                }
            }
            
            Text(LanguageHelper.isEnglish ? "Boost visibility" : "تمييز زيادة المشاهدات")
                .font(
                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 16)
                        .weight(.semibold)
                )
        }
        .padding(.horizontal)
        .padding(.bottom, 8)
    }
}

// MARK: - Selected Plan Summary
struct SelectedPlanSummaryView: View {
    @ObservedObject var viewModel: MicroDealerViewModel
    
    var body: some View {
        VStack(spacing: 0) {
            Divider()
                .background(Color(uiColor: UIColor.hexStringToUIColor(hex: "#F2F4F7")))
            
            HStack {
                Text("\(LanguageHelper.isEnglish ? "\(viewModel.selectedPackageBundle?.numberOfAds ?? 0) Cars" : "\(viewModel.selectedPackageBundle?.numberOfAds ?? 0) سيارات")")
                    .font(
                        Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                            .weight(.semibold)
                    )
                    .foregroundColor(.black)
                
                Spacer()
                
                HStack(spacing: 4) {
                    HStack(spacing: 2) {
                        Text("\(viewModel.selectedPackageBundle?.bundleCarPrice?.formattedWithWithoutFraction() ?? "") \("KWD".localized)")
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                    .weight(.semibold)
                            )
                            .foregroundColor(Color.init(uiColor: Colors.slateColor))
                        
                        Text(LanguageHelper.isEnglish ? "/car" : "/سيارة")
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                    .weight(.regular)
                            )
                            .foregroundColor(Color.init(uiColor: Colors.slateColor))
                    }
                    .overlay(alignment: .bottom) {
                        Rectangle()
                            .stroke(
                                Color(
                                    uiColor: UIColor.hexStringToUIColor(hex: "#FDB022")
                                ),
                                style: StrokeStyle(lineWidth: 1, dash: [2, 2])
                            )
                            .frame(height: 1)
                    }
                    
                    Text("\(viewModel.selectedPackageBundle?.price?.formattedWithWithoutFraction() ?? "") \("KWD".localized)")
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                .weight(.bold)
                        )
                        .foregroundColor(.black)
                }
                
                Button(action: {
                    viewModel.goBack()
                }) {
                    Image("EditSSS")
                        .resizable()
                        .frame(width: 32, height: 32)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical,20)
        }
        .background(Color.white)
        .shadow(color: Color(red: 0.07, green: 0.07, blue: 0.07).opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - Intro Section
struct IntroSectionView: View {
    var body: some View {
        VStack(spacing: 16) {
            HStack(spacing: 12) {
                Image("dfdfg4")
                    .resizable()
                    .frame(width: 44, height: 44)
                
                VStack(alignment: .leading, spacing: 4) {
					Text(LanguageHelper.isEnglish ? "Boost your reach" : "زيد عدد المشاهدات")
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 16)
                                .weight(.semibold)
                        )
                        .foregroundColor(.black)
                    
					Text(LanguageHelper.isEnglish ? "Maximize your ad visibility, attract serious buyers and sell faster!" : "استهدف شريحة أكبر، واجذب المشترين الجديين وبيع سيارتك أسرع!")
                        .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12))
                        .foregroundColor(Color.init(uiColor: Colors.slateColor))
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 24)
        }
        .background(Color(UIColor.hexStringToUIColor(hex: "#FFFAEB")))
        .padding(.bottom, 8)
    }
}

// MARK: - Boost Options
struct BoostOptionsView: View {
    @ObservedObject var viewModel: MicroDealerViewModel
    
    var body: some View {
        VStack(spacing: 32) {
            if let packages = viewModel.boostPackages?.Packages {
                ForEach(packages, id: \.Id) { package in
                    BoostOptionCard(
                        isSelected: viewModel.selectedBoostPackage?.Id == package.Id,
                        title: package.Title ?? "",
                        price: package.Price?.formattedWithWithoutFraction() ?? "",
                        features: package.Boosters ?? []
                    ) {
                        viewModel.selectedBoostPackage = package
                    }
                }
            }
        }
    }
}

// MARK: - Boost Option Card
struct BoostOptionCard: View {
    let isSelected: Bool
    let title: String
    let price: String
    let features: [BoosterModel]
    let onTap: () -> Void
    
    var body: some View {
        ZStack(alignment: .topLeading) {
            VStack(spacing: 16) {
                HStack(alignment: .top) {
                    featuresList
                        .padding(.bottom, 16)
                    
                    VStack(spacing: 8) {
                        Text(price + " " + "KWD".localized)
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                    .weight(.bold)
                            )
                            .foregroundColor(.black)
                        
                        Circle()
                            .stroke(isSelected ? Color(uiColor: Colors.bluishColor) : Color.gray.opacity(0.4), lineWidth: 1)
                            .frame(width: 20, height: 20)
                            .overlay(
                                Circle()
                                    .fill(Color(uiColor: Colors.bluishColor))
                                    .frame(width: 15, height: 15)
                                    .opacity(isSelected ? 1 : 0)
                            )
                    }
                }
                .padding(.top, LanguageHelper.isEnglish ? 24 : 34)
            }
            .padding(.horizontal, 20)
            .background(Color.white)
            .cornerRadius(12)
            
            .shadow(color: Color(red: 0.95, green: 0.96, blue: 0.97), radius: 0, x: 0, y: 4)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .inset(by: 0.5)
                    .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), lineWidth: 1)
            )
            .onTapGesture(perform: onTap)
            
            Text(title)
                .font(
                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                        .weight(.semibold)
                )
                .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                .padding(.horizontal, 10)
                .padding(.vertical, 6)
                .background(Color.white)
                .cornerRadius(100)
                .overlay(
                    RoundedRectangle(cornerRadius: 100)
                        .inset(by: 0.5)
                        .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), lineWidth: 1)
                )
                .padding(.leading, 20)
                .offset(y: -14)
        }
    }
    
    private var featuresList: some View {
        VStack(alignment: .leading, spacing: 6) {
            ForEach(features, id: \.Description) { feature in
                HStack(spacing: 12) {
                    KFImage(URL(string: feature.Icon ?? ""))
                        .placeholder {
                            Image("no-image")
                                .resizable()
                                .frame(width: 24, height: 24)
                                .scaledToFit()
                        }
                        .resizable()
                        .frame(width: 26, height: 26)
                    
                    boldedNumberText(text: feature.Description ?? "")
                        .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14))
                        .foregroundColor(.black)
                    
                    Spacer()
                }
            }
        }
    }
}

// MARK: - Info Note
struct InfoNoteView: View {
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "info.circle")
                .font(.system(size: 16))
                .foregroundColor(.black)
            
            Text(LanguageHelper.isEnglish ? "Each listing will receive the full benefits of your chosen boost option." : "كل إعلان سيحصل على الميزات الكاملة لخيار التمييز الذي اخترته.")
                .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12))
                .foregroundColor(.black)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
        .padding(16)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Bottom Section
struct BottomSectionView: View {
    @ObservedObject var viewModel: MicroDealerViewModel
    
    private var totalPrice: String {
        return ((viewModel.selectedPackageBundle?.price ?? 0) + (viewModel.selectedBoostPackage?.Price ?? 0)).formattedWithWithoutFraction()
    }
    
    private struct CurrencyText: View {
        let amount: String
        var body: some View {
            HStack(spacing: 4) {
                if LanguageHelper.isEnglish {
                    Text(amount)
                    Text("KWD")
                } else {
                    Text("د.ك")
                    Text(amount)
                }
            }
            .environment(\.layoutDirection, .leftToRight)
        }
    }
    
    var body: some View {
        VStack(spacing: 8) {
			HStack(alignment: .top) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Total".localized)
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                .weight(.semibold)
                        )
                        .foregroundColor(.black)
                    
                    Text(LanguageHelper.isEnglish
                         ? "\(viewModel.selectedPackageBundle?.numberOfAds ?? 0) Cars + \(viewModel.selectedBoostPackage?.Title ?? "")"
                        : "\(viewModel.selectedPackageBundle?.numberOfAds ?? 0) سيارات + \(viewModel.selectedBoostPackage?.Title ?? "")")
                        .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12))
                        .foregroundColor(.gray)
                }
                
                Spacer()
                
                CurrencyText(amount: totalPrice)
                    .font(
                        Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 16)
                            .weight(.bold)
                    )
                    .foregroundColor(.black)
            }
            
            HStack(spacing: 12) {
                Button(LanguageHelper.isEnglish ? "Skip" : "تخطى") {
                    viewModel.skipBoost()
                }
                .font(
                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 16)
                        .weight(.semibold)
                )
                .foregroundColor(Color.init(uiColor: Colors.slateColor))
                .frame(maxWidth: .infinity)
                .frame(height: 40)
                .background(Color(.white))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .inset(by: 1)
                        .stroke(Color.init(uiColor: UIColor.hexStringToUIColor(hex: "#EAECF0")), lineWidth: 1)
                )
                
                Button {
                    viewModel.continueBoost()
                } label: {
                    Text("Continue".localized)
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 16)
                                .weight(.semibold)
                        )
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 40)
                        .background(Color.init(uiColor: Colors.shamrockColor))
                        .cornerRadius(8)
                }
            }
            .padding(.top, 4)
        }
        .padding(.horizontal, 20)
        .padding(.top, 16)
        .padding(.bottom, 16)
        .background(Color.white)
        .overlay(
            LinearGradient(
                gradient: SwiftUI.Gradient(colors: [Color.black.opacity(0.12), Color.black.opacity(0.0)]),
                startPoint: .bottom,
                endPoint: .top
            )
            .frame(height: 8)
            .allowsHitTesting(false),
            alignment: .top
        )
    }
}

struct HeightPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = max(value, nextValue())
    }
}
