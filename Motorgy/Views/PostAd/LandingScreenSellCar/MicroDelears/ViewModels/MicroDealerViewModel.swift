//
//  MicroDealerViewModel.swift
//  Motorgy
//
//  Created by <PERSON> on 03/06/2025.
//  Copyright <PERSON>. All rights reserved.
//

import Foundation
import SwiftUI

class MicroDealerViewModel: ObservableObject {
    @Published var selectedPackageBundle: PackageBundelModel?
    @Published var packageBundles: [PackageBundelModel] = []
    
    @Published var selectedBoostPackage: PackageBoostModel?
    @Published var boostPackages: BoostPackagesModel?
    
    @Published var activeSubscriptions: [BundleModelMyCars] = []
    @Published var isApiLoaded = false
    
    private weak var navigationController: UINavigationController?
    private var carRepository = CarRepositry()
    
    init(navigationController: UINavigationController?) {
        self.navigationController = navigationController
    }
    
    func goBack() {
        navigationController?.popViewController(animated: true)
    }
    
    func cancelCompletely() {
        guard let nav = navigationController else { return }
        
        let alert = UIAlertController(title: nil, message:"Are you sure you want to exit?".localized, preferredStyle: .alert)
        
        let yesAction = UIAlertAction(title: "Yes".localized, style: .default) { _ in
            nav.popToRootViewController(animated: true)
        }
        
        let cancelAction = UIAlertAction(title: "Cancel".localized, style: .cancel) { _ in }
        
        let messageAttrString = NSMutableAttributedString(
            string: "Are you sure you want to exit?".localized,
            attributes: [
                NSAttributedString.Key.font: UIFont(name: LanguageHelper.language.currentLanguage() == "en" ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14.0)!
            ]
        )
        
        alert.setValue(messageAttrString, forKey: "attributedMessage")
        
        alert.addAction(yesAction)
        alert.addAction(cancelAction)
        
        let presenter = nav.topViewController ?? nav
        presenter.present(alert, animated: true, completion: nil)
    }
    
    func onContinueBundle() {
        guard let navigationController = navigationController else { return }
        SellingMultipleRouter.moveToBoostVisibility(from: navigationController, viewModel: self)
    }
    
    func getContact() {
        let contactUsVC = UIStoryboard(name: "Account", bundle: nil).instantiateViewController(withIdentifier: "ContactUsVC") as! ContactUsVC

        // Set the current view controller's back bar button item to hide text
        if let currentVC = navigationController?.topViewController {
            currentVC.navigationItem.backBarButtonItem = UIBarButtonItem(title: "", style: .plain, target: nil, action: nil)
        }

        if #available(iOS 18.0, *) {
            navigationController?.setNavigationBarHidden(false, animated: false)
            navigationController?.navigationItem.backButtonTitle = ""
        } else if #available(iOS 17.0, *) {
            navigationController?.navigationBar.isHidden = false
            navigationController?.navigationItem.backButtonTitle = ""
        } else {
            navigationController?.setNavigationBarHidden(false, animated: false)
            navigationController?.navigationItem.backButtonTitle = ""
        }
        navigationController?.pushViewController(contactUsVC, animated: true)
    }
    
    func getHelp() {
        let contactUsVC = UIStoryboard(name: "Account", bundle: nil).instantiateViewController(withIdentifier: "ContactUsVC") as! ContactUsVC

        // Set the current view controller's back bar button item to hide text
        if let currentVC = navigationController?.topViewController {
            currentVC.navigationItem.backBarButtonItem = UIBarButtonItem(title: "", style: .plain, target: nil, action: nil)
        }

        if #available(iOS 18.0, *) {
            navigationController?.setNavigationBarHidden(false, animated: false)
            navigationController?.navigationItem.backButtonTitle = ""
        } else if #available(iOS 17.0, *) {
            navigationController?.navigationBar.isHidden = false
            navigationController?.navigationItem.backButtonTitle = ""
        } else {
            navigationController?.setNavigationBarHidden(false, animated: false)
            navigationController?.navigationItem.backButtonTitle = ""
        }
        navigationController?.pushViewController(contactUsVC, animated: true)
    }
	
	func buySingleCar() {
		ConstantsValues.sharedInstance.isOpenBoostVisibilityMicroDealerScreenFromPackages = false
		self.navigationController?.popViewController(animated: true)
	}
    
    func skipBoost() {
        guard let navigationController = navigationController else { return }
        self.selectedBoostPackage = nil
        SellingMultipleRouter.moveToBuyBundleForMicroDealerCheckout(from: navigationController, viewModel: self)
    }
    
    func continueBoost() {
        guard let navigationController = navigationController else { return }
        SellingMultipleRouter.moveToBuyBundleForMicroDealerCheckout(from: navigationController, viewModel: self)
    }
    
    func getPackageBundlesDetails(isBundle: Bool, includeSelfService: Bool, completion: (() -> Void)? = nil) {
        self.carRepository.getPackageBundlesDetails(isBundle: isBundle, includeSelfService: includeSelfService) {  [weak self] result in
            DispatchQueue.main.async {
                self?.packageBundles = result ?? []
                self?.isApiLoaded = result?.count ?? 0 > 0

                if self?.selectedPackageBundle == nil {
                    self?.selectedPackageBundle = self?.packageBundles.first
                }

                completion?()
            }
        }
    }
    
    func getBoostPackages() {
        self.carRepository.getBoostPackages() { [weak self] result in
            DispatchQueue.main.async {
                self?.boostPackages = result

                if self?.selectedBoostPackage == nil {
                    self?.selectedBoostPackage = self?.boostPackages?.Packages?.first
                }
            }
        }
    }

    func setSelectedPackageBundleForRenew(packageId: Int) {
        let matchingPackage = self.packageBundles.first { $0.packageId == packageId }
        self.selectedPackageBundle = matchingPackage

        print("🔄 MicroDealerViewModel: Setting selected package for renew")
        print("📦 Package ID to find: \(packageId)")
        print("📋 Available packages: \(self.packageBundles.map { "\($0.packageId ?? 0): \($0.packageName ?? "")" })")
        print("✅ Selected package: \(matchingPackage?.packageName ?? "None found")")
    }
    
    public func buyBundleMicroDealerFlow(id: Int, boostPackageId: Int, paymentType: Int, packageId: Int, discountCodeId: Int? = nil, useWallet: Bool, isRenew: Bool? = false, completion: @escaping (Result?) -> Void) {
        self.carRepository.buyBundleMicroDealerFlow(id: id, useWallet: useWallet, packageId: packageId, boostPackageId: boostPackageId, discountCodeId: discountCodeId, paymentType: paymentType, isRenew: isRenew) { result in
            completion(result)
        }
    }
    
    public func validateDiscountCode(code: String, estimatedPrice: Int, packageId: Int, completion: @escaping (Result?) -> Void) {
        self.carRepository.sycPromoCodeValidateForMicroDealerBuyBundleFlow(code: code, estimatedPrice: estimatedPrice, packageId: packageId) { result in
            completion(result)
        }
    }
    
    public func getActiveSubscriptionsForMicroDealer(completion: @escaping (Result?) -> Void) {
        self.carRepository.getActiveSubscriptionsForMicroDealer { result in
            DispatchQueue.main.async {
                self.activeSubscriptions = result?.activeSubscriptions ?? []
            }
            completion(result)
        }
    }
}
