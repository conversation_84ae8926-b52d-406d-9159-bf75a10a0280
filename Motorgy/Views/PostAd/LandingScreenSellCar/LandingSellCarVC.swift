//
//  LandingSellCar.swift
//  Motorgy
//
//  Created by <PERSON><PERSON> on 01/06/2021.
//  Copyright © 2021 <PERSON><PERSON>. All rights reserved.
//

import UIKit
import RxSwift
import RxCocoa
import FirebaseAnalytics
import FBSDKCoreKit
import SmartlookAnalytics
import AppsFlyerLib
import SwiftUI

public var trigger = ""

class LandingSellCarVC: BaseVC, OnDismiss {

    // MARK: - Outlets and Values
    @IBOutlet weak var tv: UITableView!
    @IBOutlet weak var containerView: UIView!
    @IBOutlet weak var sellCarNowBtn: UIButton!
    @IBOutlet weak var containerViewCons: NSLayoutConstraint!
    @IBOutlet weak var tableViewBottomCons: NSLayoutConstraint!
    
    private let getLandingSellCarVM = LanddingSellCarVM()
    private var getLandingSellCarTVHandler: LandingSellCarTVHandler!
    private var postAdVM = PostAdViewModel()
    private var userData = [String : Any]()
    private var moveToSellingSteps: Bool = false, brandid: String = "", modelid: String = ""
    private var carDetailsVM = CarDetailsViewModel()
    private var isGuest: Bool?
    private var isTrim: Bool = false
    private var microDealerViewModel: MicroDealerViewModel?
    private var hasUserDeclinedBundle = false

    // MARK: - viewDidLoad
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        NotificationCenter.default.addObserver(self, selector: #selector(refreshScreen), name: NSNotification.Name("AbandonedRequestSent"), object: nil)
        
        self.tabBarController?.delegate = self
        
        getData()
        
        self.tabBarController?.navigationItem.backButtonTitle = " "

        containerView.isHidden = true
        containerViewCons.constant = 0
        tableViewBottomCons.constant = 20
        
        self.containerView.makeCorner(cornerRadius: 16, isShaddow: true, borderColor: .white, borderWidth: 1)
        self.containerView.addTopShadow(shadowColor: UIColor.gray, shadowOpacity: 0.08, shadowRadius: 5, offset: CGSize(width: 0.0, height : -5.0))
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("AbandonedRequestSent"), object: nil)
    }
    
    @objc
    private func refreshScreen() {
        self.getData()
    }
    
    // MARK: - viewWillAppear
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        isFirstTimeLoad = true
        isFirstMileageTimeLoad = true
        isFirstPriceTimeLoad = true
        
        ConstantsValues.sharedInstance.isCallBackApiAlreadyCalled = false
        
        self.tabBarController?.navigationItem.leftBarButtonItems = []
        self.tabBarController?.navigationItem.backButtonTitle = " "
        self.parent?.navigationItem.titleView = nil
        
        if #available(iOS 18.0, *) {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        } else if #available(iOS 17.0, *) {
            self.navigationController?.navigationBar.isHidden = false
        } else {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        }
        
        #if DEVELOPMENT
        #else
        Analytics.logEvent("sell_car_landing_screen", parameters: [:])
        AppsFlyerLib.shared().logEvent("sell_car_landing_screen", withValues: [:])
        if trigger == "" {
            Analytics.logEvent("sell_car_start", parameters: ["trigger" : "main_navigation_bar"])
//            AppEvents.shared.logEvent(AppEvents.Name.init(rawValue: "sell_car_start"), parameters: [AppEvents.ParameterName("trigger"): "main_navigation_bar"])
        }
        #endif
        
        DispatchQueue.main.async {
            self.configureTitle(fontSize: 16.0, color: Colors.charcoalColor)
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.postAdVM.getCarDetails().bind{ [weak self] in
                self?.setupAbandonedPushNotificationMoves()
            }.disposed(by: self.postAdVM.getDisposeBag())
            
            if self.postAdVM.getCarDetails() != nil {
                self.setupAbandonedPushNotificationMoves()
            }
        }
        
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        self.tabBarController?.navigationItem.backButtonTitle = " "
    }
    
    func configureTitle(fontSize: CGFloat, color: UIColor) {
        if #available(iOS 17.0, *) {
            let en = UIImage(named: "back")?.withRenderingMode(.alwaysOriginal)
            let ar = UIImage(named: "right-back")?.withRenderingMode(.alwaysOriginal)
            
            let backButtonImage = LanguageHelper.language.currentLanguage() == "en"
                ? Locale.current.languageCode == "ar" ? ar : en
                : Locale.current.languageCode == "ar" ? en : ar
            
            let navBarAppearance = UINavigationBarAppearance()
            navBarAppearance.configureWithOpaqueBackground()
            navBarAppearance.shadowColor = .clear
            navBarAppearance.shadowImage = UIImage()
            navBarAppearance.titleTextAttributes = [
                NSAttributedString.Key.font: UIFont(name: LanguageHelper.language.currentLanguage() == "en" ? "Inter-SemiBold" : "Cairo-SemiBold", size: fontSize)!,
                NSAttributedString.Key.foregroundColor: color
            ]
            navBarAppearance.setBackIndicatorImage(backButtonImage, transitionMaskImage: backButtonImage)
            
            self.tabBarController?.navigationController?.navigationBar.backIndicatorImage = backButtonImage
            self.tabBarController?.navigationController?.navigationBar.backIndicatorTransitionMaskImage = backButtonImage
            self.tabBarController?.navigationController?.navigationBar.standardAppearance = navBarAppearance
            self.tabBarController?.navigationController?.navigationBar.scrollEdgeAppearance = navBarAppearance
        } else if #available(iOS 13.0, *) {
            let navBarAppearance = UINavigationBarAppearance()
            navBarAppearance.configureWithOpaqueBackground()
            navBarAppearance.shadowColor = .clear
            navBarAppearance.shadowImage = UIImage()
            navBarAppearance.titleTextAttributes = [
                NSAttributedString.Key.font: UIFont(name: LanguageHelper.language.currentLanguage() == "en" ? "Inter-SemiBold" : "Cairo-SemiBold", size: fontSize)!,
                NSAttributedString.Key.foregroundColor: color
            ]
            
            self.tabBarController?.navigationController?.navigationBar.standardAppearance = navBarAppearance
            self.tabBarController?.navigationController?.navigationBar.scrollEdgeAppearance = navBarAppearance
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        self.hasUserDeclinedBundle = false
        
        self.title = "Sell a car".localized
        self.parent?.title = "Sell a car".localized
        self.tabBarController?.navigationItem.backButtonTitle = " "
        self.tabBarController?.tabBar.isHidden = false
    }
    
    // MARK: - viewWillDisappear
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        trigger = ""
        self.navigationItem.rightBarButtonItem = nil
        self.parent?.navigationItem.rightBarButtonItem = nil
        
        DispatchQueue.main.async {
            self.configureTitle(fontSize: 14.0, color: Colors.navyColor)
        }
    }
    
    // MARK: - getData
    func getData() {
        getLandingSellCarTVHandler = LandingSellCarTVHandler(
            tv: tv,
            controller: self,
            viewModel: getLandingSellCarVM,
            landingVC: self,
            postAdVM: postAdVM
        )
        
        getLandingSellCarTVHandler.delegate = self
        
        self.getLandingSellCarVM.getLandingSellCar().bind { [weak self] _ in
            self?.getLandingSellCarTVHandler.showData(landingSellList: self?.getLandingSellCarVM.getLandingSellCarResult() ?? LandingSellCarModel())
            
            DispatchQueue.main.async {
                if self?.getLandingSellCarVM.getLandingSellCarResult()?.abandonedRequest == nil || self?.getLandingSellCarVM.getLandingSellCarResult()?.abandonedRequest?.AbandonedRequestId == 0 {
                    self?.sellCarNowBtn.setTitle("Sell my car".localized, for: .normal)
                } else {
                    self?.sellCarNowBtn.setTitle("Continue selling".localized, for: .normal)
                }
            }
        }.disposed(by: self.getLandingSellCarVM.getDisposeBag())
    }
    
    // MARK: - setupFromAbandonedPushNotifications
    func setupFromAbandonedPushNotifications(moveToSellingSteps: Bool, brandid: String, modelid: String, type: String) {
        self.moveToSellingSteps = moveToSellingSteps
        self.brandid = brandid
        self.modelid = modelid
        self.isGuest = type == "61" ? true : false
    }
    
    // MARK: - setupAbandonedPushNotificationMoves
    private func setupAbandonedPushNotificationMoves() {
        if self.isGuest ?? false {
            if UserHelper.user.isLogin() {
                self.moveToStepsWithData()
            } else {
                self.moveToLogin()
            }
        } else {
            self.moveToStepsWithData()
        }
    }
    
    private func checkForMobileNumberFirst() {
        let validationVC = self.getNextViewController(viewControllerClass: MobileNumberValidationVC.self, storyBoardName: "Authentication", identifier: "MobileNumberValidationVC") ?? MobileNumberValidationVC()
        validationVC.modalPresentationStyle = .custom
        self.present(validationVC, animated: true)
    }
    
    // MARK: - moveToStepsWithData
    private func moveToStepsWithData() {
        let mobileNumber = SharedHelper.shared.getFromDefault(key: "mobile")
        
        if self.moveToSellingSteps == true {
            self.moveToSellingSteps = false
            
            if mobileNumber.isEmpty || mobileNumber == "" || mobileNumber == "********" {
                self.checkForMobileNumberFirst()
            } else {
                if self.brandid != "" {
                    var type = "Model".localized
                    var data: [Any]?
                    
                    let brandName = BrandModelResult.shared.self?.lstBrandModelYear?.first(where: { $0.brandID == Int(self.brandid) ?? 0 })?.brandName ?? ""
                    let brandIndex = BrandModelResult.shared.self?.lstBrandModelYear?.firstIndex(where: { $0.brandID == Int(self.brandid) ?? 0}) ?? 0
                    
                    data = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels
                    
                    userData["0"] = DataAttribute(name: brandName, id: Int(self.brandid) ?? 0, key: "Make".localized, dataKey: "BrandID")
                    
                    if self.modelid != "" {
                        
                        let modelName = BrandModelResult.shared.self?.lstBrandModelYear?.first(where: { $0.brandID == Int(self.brandid) ?? 0 })?.lstModels?.first(where: { $0.modelID == Int(self.modelid) ?? 0 })?.modelName ?? ""
                        
                        userData["1"] = DataAttribute(name: modelName, id: Int(self.modelid) ?? 0, key: "Model".localized, dataKey: "ModelID")
                        
                        let modelIndex = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?.firstIndex(where: { $0.modelID == Int(self.modelid) ?? 0 }) ?? 0
                        
                        if BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?[modelIndex].lstTrims?.count ?? 0 > 1 {
                            self.isTrim = true
                            data = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?[modelIndex].lstTrims
                            type = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?[modelIndex].lstTrims?[0].type ?? ""
                        } else {
                            self.isTrim = false
                            data = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?[modelIndex].lstYear
                            type = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?[modelIndex].nextType ?? ""
                        }
                    }
                    
                    self.moveToCarDetails(isFirst: true, type: type, data: data)
                }
            }
        }
    }
    
    // MARK: - moveToPostAd
//    private func moveToPostAd() {
//        let postAdVC = self.getNextViewController(viewControllerClass: PostAdVC.self, storyBoardName: "SellYouCar", identifier: "PostAdVC") ?? PostAdVC()
//        postAdVC.setDismissListner(onDismiss: self)
//        self.navigationController?.pushViewController(postAdVC, animated: false)
//    }
    
    // MARK: - dismissed
    
    internal func dismissed() {
        self.tabBarController?.selectedIndex = 2
    }
     
    @IBAction func sellCarAction(_ sender: Any) {
        if self.getLandingSellCarVM.getLandingSellCarResult()?.abandonedRequest == nil || self.getLandingSellCarVM.getLandingSellCarResult()?.abandonedRequest?.AbandonedRequestId == 0 {
			#if DEVELOPMENT
            #else
            Analytics.logEvent("sell_car_landing_click", parameters: ["trigger":"\(trigger == "" ? "main_navigation_bar" : trigger)"])
            //        AppEvents.shared.logEvent(
            //            AppEvents.Name.init(rawValue: "sell_car_landing_click"),
            //            parameters: [
            //                AppEvents.ParameterName("trigger"):"\(trigger == "" ? "main_navigation_bar" : trigger)"
            //            ]
            //        )
            trigger = ""
			#endif
            
            self.startSellingProcess()
        } else {
            self.continueSellingAction()
        }
    }
    
    private func moveToCarDetails(isFirst: Bool, type: String?, data: [Any]?) {
        let carDetailsVC = self.getNextViewController(viewControllerClass: CarDetailsVC.self,storyBoardName: "SellYouCar", identifier: "CarDetailsVC") ?? CarDetailsVC()
        carDetailsVC.setData(
            brandModelResult: BrandModelResult.shared.self,
            onDismiss: self,
            isFirst: isFirst,
            type: type,
            data: data,
            userData: userData,
            serviceId: 0,
            isTrim: self.isTrim
        )
        carDetailsVC.setInspectionAndPackagesData(
            lstInspectionLocations: self.getLandingSellCarVM.getLandingSellCarResult()?.lstInspectionLocations,
            lstCityWithAreas: self.getLandingSellCarVM.getLandingSellCarResult()?.lstCityWithAreas,
            lstMasterPackages: self.getLandingSellCarVM.getLandingSellCarResult()?.lstMasterPackages
        )
        carDetailsVC.setMovingForwardFromLanding(movingForwardFromLanding: true)
        self.navigationController?.pushViewController(carDetailsVC, animated: true)
    }
}

extension LandingSellCarVC {
    
    func postAbandonedRequest(brandid: Int) {
        if brandid != 0 {
            SharedHelper.shared.saveIntValueInDefault(key: "CarDetailsPNModelBrandID", value: 0)
            
            self.carDetailsVM.postAbandonedRequest(brandID: brandid, modelID: 0).bind { result in
                if ConstantsValues.sharedInstance.abandonedRequestID == 0 {
                    ConstantsValues.sharedInstance.abandonedRequestID = result?.abandonedRequestID ?? 0
                }
            }.disposed(by: self.carDetailsVM.getDisposeBag())
        }
    }
}

extension LandingSellCarVC: LandingSellCarTVHandlerDelegate {
    func editAbandonedAction() {
        self.startSellingProcess()
    }
    
    func moveToModelList(brandId: Int) {
        if UserHelper.user.isLogin() {
            let mobileNumber = SharedHelper.shared.getFromDefault(key: "mobile")
            
            if mobileNumber.isEmpty || mobileNumber == "" || mobileNumber == "********" {
                self.checkForMobileNumberFirst()
            } else {
                if self.getLandingSellCarVM.landingSellCar?.haveAnyActiveSubscription ?? false && !hasUserDeclinedBundle {
                    self.moveToFirstConfirmDialougeForMicroDealer(brandId: brandId, isComingFromAbandoned: false)
                } else {
                    self.moveToModelsList(brandID: brandId)
                }
            }
        } else {
            self.moveToLogin()
        }
    }
    
    private func moveToModelsList(brandID: Int) {
        let type = "Model".localized
        var userData = [String : Any]()
        var data: [Any]?
        
        let brandName = BrandModelResult.shared.self?.lstBrandModelYear?.first(where: { $0.brandID == brandID })?.brandName ?? ""
        let brandIndex = BrandModelResult.shared.self?.lstBrandModelYear?.firstIndex(where: { $0.brandID == brandID }) ?? 0
        
        data = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels
        
        userData = ["0": DataAttribute(name: brandName, id: brandID, key: "Make".localized, dataKey: "BrandID")]
        
#if DEVELOPMENT
#else
        Analytics.logEvent("sell_car_make", parameters: ["value" : brandName])
#endif
        
        DispatchQueue.main.async { [weak self] in
            let carDetailsVCMake = self?.getNextViewController(viewControllerClass: CarDetailsVC.self,storyBoardName: "SellYouCar", identifier: "CarDetailsVC") ?? CarDetailsVC()
            carDetailsVCMake.setData(
                brandModelResult: BrandModelResult.shared.self,
                onDismiss: self,
                isFirst: true,
                type: BrandModelResult.shared.self?.type,
                data: BrandModelResult.shared.self?.lstBrandModelYear,
                userData: userData,
                serviceId: 0
            )
            
            carDetailsVCMake.setInspectionAndPackagesData(
                lstInspectionLocations: self?.getLandingSellCarVM.getLandingSellCarResult()?.lstInspectionLocations,
                lstCityWithAreas: self?.getLandingSellCarVM.getLandingSellCarResult()?.lstCityWithAreas,
                lstMasterPackages: self?.getLandingSellCarVM.getLandingSellCarResult()?.lstMasterPackages
            )
            carDetailsVCMake.setMovingForwardFromLanding(movingForwardFromLanding: true)
            
            let carDetailsVCModel = self?.getNextViewController(viewControllerClass: CarDetailsVC.self,storyBoardName: "SellYouCar", identifier: "CarDetailsVC") ?? CarDetailsVC()
            
            carDetailsVCModel.setData(
                brandModelResult: BrandModelResult.shared.self,
                onDismiss: self,
                isFirst: true,
                type: type,
                data: data,
                userData: userData,
                serviceId: 0
            )
            carDetailsVCModel.setInspectionAndPackagesData(
                lstInspectionLocations: self?.getLandingSellCarVM.getLandingSellCarResult()?.lstInspectionLocations,
                lstCityWithAreas: self?.getLandingSellCarVM.getLandingSellCarResult()?.lstCityWithAreas,
                lstMasterPackages: self?.getLandingSellCarVM.getLandingSellCarResult()?.lstMasterPackages
            )
            carDetailsVCModel.setMovingForwardFromLanding(movingForwardFromLanding: true)
            
            UIView.performWithoutAnimation {
                if let navigationController = self?.navigationController {
                    navigationController.pushViewController(carDetailsVCMake, animated: false)
                    var viewControllers = navigationController.viewControllers
                    viewControllers.append(carDetailsVCModel)
                    navigationController.setViewControllers(viewControllers, animated: true)
                }
            }
        }
    }
    
    private func moveToCarDetailsAbandoned(isFirst: Bool, type: String?, data: [Any]?, userData: [String: Any]?, isTrim: Bool) {
        let carDetailsVC = self.getNextViewController(viewControllerClass: CarDetailsVC.self,storyBoardName: "SellYouCar", identifier: "CarDetailsVC") ?? CarDetailsVC()
        carDetailsVC.setData(
            brandModelResult: BrandModelResult.shared.self,
            onDismiss: self,
            isFirst: isFirst,
            type: type,
            data: data,
            userData: userData,
            serviceId: 0,
            isTrim: isTrim
        )
        carDetailsVC.setInspectionAndPackagesData(
            lstInspectionLocations: self.getLandingSellCarVM.getLandingSellCarResult()?.lstInspectionLocations,
            lstCityWithAreas: self.getLandingSellCarVM.getLandingSellCarResult()?.lstCityWithAreas,
            lstMasterPackages: self.getLandingSellCarVM.getLandingSellCarResult()?.lstMasterPackages
        )
        carDetailsVC.setMovingForwardFromLanding(movingForwardFromLanding: true)
        self.navigationController?.pushViewController(carDetailsVC, animated: true)
    }
    
    private func continueSellingProcessFromAbandoned() {
        let brandId = self.getLandingSellCarVM.getLandingSellCarResult()?.abandonedRequest?.BrandId ?? 0
        let brandName = BrandModelResult.shared.self?.lstBrandModelYear?.first(where: { $0.brandID == brandId })?.brandName ?? ""
        let brandIndex = BrandModelResult.shared.self?.lstBrandModelYear?.firstIndex(where: { $0.brandID == brandId }) ?? 0
        
        let modelId = self.getLandingSellCarVM.getLandingSellCarResult()?.abandonedRequest?.ModelId ?? 0
        let modelName = BrandModelResult.shared.self?.lstBrandModelYear?.first(where: { $0.brandID == brandId })?.lstModels?.first(where: { $0.modelID == modelId })?.modelName ?? ""
        let modelIndex = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?.firstIndex(where: { $0.modelID == modelId }) ?? 0
        
        var trimId: Int? = nil
        let rawTrimId = self.getLandingSellCarVM.getLandingSellCarResult()?.abandonedRequest?.TrimId
        let trimName = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?.first(where: { $0.modelID == modelId })?.lstTrims?.first(where: { $0.TrimId == rawTrimId })?.TrimName ?? ""
        let trimIndex = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?[modelIndex].lstTrims?.firstIndex(where: { $0.TrimId == rawTrimId }) ?? 0
        
        if let intTrimId = rawTrimId as? Int {
            trimId = intTrimId
        } else if let stringTrimId = rawTrimId as? String, let intFromString = Int(stringTrimId) {
            trimId = intFromString
        } else if let stringTrimId = rawTrimId as? String, stringTrimId == "<null>" {
            trimId = nil
        }
        
        let year = self.getLandingSellCarVM.getLandingSellCarResult()?.abandonedRequest?.Year ?? 0
        let mileage = self.getLandingSellCarVM.getLandingSellCarResult()?.abandonedRequest?.Mileage ?? 0
        let price = self.getLandingSellCarVM.getLandingSellCarResult()?.abandonedRequest?.EstimatedPrice ?? 0
        let isTrim = trimId != nil && trimId != 0
        var userData: [String: Any] = [:]
        
        ConstantsValues.sharedInstance.abandonedRequestID = self.getLandingSellCarVM.getLandingSellCarResult()?.abandonedRequest?.AbandonedRequestId ?? 0
        
        userData["0"] = DataAttribute(name: brandName, id: brandId, key: "Make".localized, dataKey: "BrandID")
        userData["1"] = DataAttribute(name: modelName, id: modelId, key: "Model".localized, dataKey: "ModelID")
        
        if isTrim {
            userData["2"] = DataAttribute(name: trimName, id: trimId ?? 0, key: "Trim".localized, dataKey: "TrimID")
        }
        
        userData[isTrim ? "3" : "2"] = DataAttribute(name: year > 0 ? year.description : "Year".localized, id: year, key: "Year".localized, dataKey: "Year")
        userData[isTrim ? "4" : "3"] = DataAttribute(name: "\(mileage)", id: mileage, key: LanguageHelper.isEnglish ? "Mileage" : "عداد الكيلومترات", dataKey: "Mileage")
        userData[isTrim ? "5" : "4"] = DataAttribute(name: "EstimatedPrice", id: Int(price), key: "Price".localized, dataKey: "EstimatedPrice")
        
        /// if there's price then go to packages
        if price != 0 {
            let vc = self.getNextViewController(viewControllerClass: BoardingPackagesVC.self, storyBoardName: "SelfService", identifier: "BoardingPackagesVC") ?? BoardingPackagesVC()
            vc.configureData(
                lstInspectionLocations: self.getLandingSellCarVM.getLandingSellCarResult()?.lstInspectionLocations ?? [],
                userData: userData,
                isTrim: isTrim,
                lstMasterPackages: self.getLandingSellCarVM.getLandingSellCarResult()?.lstMasterPackages
            )
            self.navigationController?.pushViewController(vc, animated: false)
            return
        }
        
        /// if there's mileage then go to price
        if mileage != 0 {
            let priceRangeVC = self.getNextViewController(viewControllerClass: PriceRangeVC.self, storyBoardName: "SellYouCar", identifier: "PriceRangeVC") ?? PriceRangeVC()
            priceRangeVC.navigationController?.navigationBar.topItem?.title = ""
            priceRangeVC.setupData(
                onDismiss: nil,
                isFirst: true,
                type: "",
                data: [],
                brandModelResult: BrandModelResult.shared.self,
                userData: userData,
                isEdit: false,
                canEdit: false,
                isTrim: isTrim,
                isEditStartedFromDashboard: false,
                isResubmitingCar: false
            )
            priceRangeVC.setInspectionAndPackagesData(
                lstPackages: nil,
                lstInspectionLocations: self.getLandingSellCarVM.getLandingSellCarResult()?.lstInspectionLocations,
                lstCityWithAreas: self.getLandingSellCarVM.getLandingSellCarResult()?.lstCityWithAreas,
                lstMasterPackages: self.getLandingSellCarVM.getLandingSellCarResult()?.lstMasterPackages
            )
            priceRangeVC.setOpenFromContinueSellingFromAbandonedSectionFromLanding(isContinueSelling: true)
            self.navigationController?.pushViewController(priceRangeVC, animated: true)
            return
        }
        
        /// if there's year then go to mileage
        if year != 0 {
            let kmVC = self.getNextViewController(viewControllerClass: KMVC.self, storyBoardName: "SellYouCar", identifier: "KMVC") ?? KMVC()
            kmVC.navigationController?.navigationBar.topItem?.title = ""
            kmVC.setupData(
                onDismiss: nil,
                isFirst: true,
                type: "Kilometers_".localized,
                data: [],
                brandModelResult: BrandModelResult.shared.self,
                userData: userData,
                isEdit: false,
                canEdit: false,
                isTrim: isTrim,
                isEditStartedFromDashboard: false,
                isResubmitingCar: false
            )
            kmVC.setInspectionAndPackagesData(
                lstPackages: nil,
                lstInspectionLocations: self.getLandingSellCarVM.getLandingSellCarResult()?.lstInspectionLocations,
                lstCityWithAreas: self.getLandingSellCarVM.getLandingSellCarResult()?.lstCityWithAreas,
                lstMasterPackages: self.getLandingSellCarVM.getLandingSellCarResult()?.lstMasterPackages
            )
            self.navigationController?.pushViewController(kmVC, animated: true)
            return
        }
        
        /// * if there's trim then go to year
        if trimId != 0 && trimId != nil {
            let type = "Year".localized
            var data: [Any]?
            
            data = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?[modelIndex].lstYear
            
            self.moveToCarDetailsAbandoned(isFirst: true, type: type, data: data, userData: userData, isTrim: isTrim)
            return
        }
        
        /// if there's make then go to model
        if brandId != 0 {
            var type = "Model".localized
            var data: [Any]?
            
            data = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels
            
            /// if there's model then go to trim/year
            if modelId != 0 {
                if BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?[modelIndex].lstTrims?.count ?? 0 > 1 {
                    self.isTrim = true
                    data = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?[modelIndex].lstTrims
                    type = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?[modelIndex].lstTrims?[0].type ?? ""
                } else {
                    self.isTrim = false
                    data = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?[modelIndex].lstYear
                    type = BrandModelResult.shared.self?.lstBrandModelYear?[brandIndex].lstModels?[modelIndex].nextType ?? ""
                }
            }
            
            self.moveToCarDetailsAbandoned(isFirst: true, type: type, data: data, userData: userData, isTrim: isTrim)
            return
        }
    }
    
    func continueSellingAction() {
        if UserHelper.user.isLogin() {
            let mobileNumber = SharedHelper.shared.getFromDefault(key: "mobile")
            
            if mobileNumber.isEmpty || mobileNumber == "" || mobileNumber == "********" {
                self.checkForMobileNumberFirst()
            } else {
                if self.getLandingSellCarVM.landingSellCar?.haveAnyActiveSubscription ?? false && !hasUserDeclinedBundle {
                    self.moveToFirstConfirmDialougeForMicroDealer(brandId: 0, isComingFromAbandoned: true)
                } else {
                    self.continueSellingProcessFromAbandoned()
                }
            }
        } else {
            self.moveToLogin()
        }
    }
}

extension LandingSellCarVC: UITabBarControllerDelegate {
    func tabBarController(_ tabBarController: UITabBarController, didSelect viewController: UIViewController) {
        SharedHelper.shared.saveIntValueInDefault(key: "CarDetailsPNModelBrandID", value: 0)
        
		if viewController is AccountVC || viewController is HomeVC || viewController is MyGarageVC {
            if #available(iOS 18.0, *) {
                self.navigationController?.setNavigationBarHidden(true, animated: true)
            } else if #available(iOS 17.0, *) {
                self.navigationController?.navigationBar.isHidden = true
            } else {
                self.navigationController?.setNavigationBarHidden(true, animated: true)
            }
        }
    }
}

extension LandingSellCarVC {
    func startSellingProcess() {
        if UserHelper.user.isLogin() {
            let mobileNumber = SharedHelper.shared.getFromDefault(key: "mobile")
            
            if mobileNumber.isEmpty || mobileNumber == "" || mobileNumber == "********" {
                self.checkForMobileNumberFirst()
            } else {
                if self.getLandingSellCarVM.landingSellCar?.haveAnyActiveSubscription ?? false && !hasUserDeclinedBundle {
                    self.moveToFirstConfirmDialougeForMicroDealer(brandId: 0, isComingFromAbandoned: false)
                } else {
                    self.moveToCarDetails(isFirst: true, type: BrandModelResult.shared.self?.type, data: BrandModelResult.shared.self?.lstBrandModelYear)
                }
            }
        } else {
            self.moveToLogin()
        }
    }
    
    private func moveToFirstConfirmDialougeForMicroDealer(brandId: Int, isComingFromAbandoned: Bool) {
        self.microDealerViewModel = MicroDealerViewModel(navigationController: self.navigationController)
        
        self.microDealerViewModel?.getActiveSubscriptionsForMicroDealer { [weak self] result in
            DispatchQueue.main.async {
                if result?.aPIStatus == 1 {
                    self?.showConfirmProceedDialog(brandId: brandId, isComingFromAbandoned: isComingFromAbandoned)
                } else {
                    if brandId > 0 {
                        self?.moveToModelsList(brandID: brandId)
                    } else if isComingFromAbandoned {
                        self?.continueSellingProcessFromAbandoned()
                    } else {
                        self?.moveToCarDetails(isFirst: true, type: BrandModelResult.shared.self?.type, data: BrandModelResult.shared.self?.lstBrandModelYear)
                    }
                }
            }
        }
    }
    
    private func showConfirmProceedDialog(brandId: Int, isComingFromAbandoned: Bool) {
        let dialogView = ConfirmProceedActiveBundleDialog(
            isPresented: .constant(true),
            onProceed: { [weak self] in
                self?.handleProceedAction(isComingFromAbandoned: isComingFromAbandoned)
            },
            onCancel: { [weak self] in
                self?.handleCancelAction(brandId: brandId, isComingFromAbandoned: isComingFromAbandoned)
            },
            onDismiss: { [weak self] in
                self?.handleDismissAction()
            }
        )
        
        let hostingController = UIHostingController(rootView: dialogView)
        hostingController.modalPresentationStyle = .overFullScreen
        hostingController.modalTransitionStyle = .crossDissolve
        hostingController.view.backgroundColor = .clear
        
        self.present(hostingController, animated: true)
    }
    
    private func handleProceedAction(isComingFromAbandoned: Bool) {
        self.hasUserDeclinedBundle = false
        
        self.dismiss(animated: true) { [weak self] in
            self?.moveToBundleSelectionDialog(microDealerViewModel: self?.microDealerViewModel, isComingFromAbandoned: isComingFromAbandoned)
        }
    }
    
    private func handleCancelAction(brandId: Int, isComingFromAbandoned: Bool) {
        self.hasUserDeclinedBundle = true
        
        self.dismiss(animated: true) { [weak self] in
            if brandId > 0 {
                self?.moveToModelsList(brandID: brandId)
            } else if isComingFromAbandoned {
                self?.continueSellingProcessFromAbandoned()
            } else {
                self?.moveToCarDetails(isFirst: true, type: BrandModelResult.shared.self?.type, data: BrandModelResult.shared.self?.lstBrandModelYear)
            }
        }
    }
    
    private func handleDismissAction() {
        self.dismiss(animated: true)
    }
    
    private func moveToBundleSelectionDialog(microDealerViewModel: MicroDealerViewModel?, isComingFromAbandoned: Bool) {
        let bundleSheet = BundleSelectionDialog(
            isPresented: .constant(true),
            bundles: microDealerViewModel?.activeSubscriptions ?? [],
            selectedBundleId: microDealerViewModel?.activeSubscriptions.first?.id ?? 0,
            onBundleSelected: { [weak self] selectedBundle in
                self?.dismiss(animated: true) {
                    ConstantsValues.sharedInstance.isListingCarInMicroDealerFlowStarted = true
                    ConstantsValues.sharedInstance.activeSubscriptionMicroDealer = selectedBundle
					
					if isComingFromAbandoned {
						self?.continueSellingProcessFromAbandoned()
					} else {
						self?.moveToCarDetails(isFirst: true, type: BrandModelResult.shared.self?.type, data: BrandModelResult.shared.self?.lstBrandModelYear)
					}
                }
            },
            onCancel: { [weak self] in
                self?.dismiss(animated: true)
            }, onDismiss: { [weak self] in
                self?.dismiss(animated: true)
            }
        )
        
        let hostingController = UIHostingController(rootView: bundleSheet)
        hostingController.modalPresentationStyle = .custom
        hostingController.view.backgroundColor = .clear
        self.present(hostingController, animated: true)
    }
}
