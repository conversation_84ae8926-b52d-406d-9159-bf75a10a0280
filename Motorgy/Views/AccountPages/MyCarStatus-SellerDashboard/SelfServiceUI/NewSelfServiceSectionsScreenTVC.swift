//
//  NewSelfServiceSectionsScreenTVC.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 13/05/2025.
//  Copyright 2025 <PERSON><PERSON>. All rights reserved.
//

import Foundation
import SwiftUI
import Kingfisher
import Lottie

class NewSelfServiceSectionsScreenTVC: UITableViewCell, CellReusableIdentifier {
    private var carStatus: CarStatusModel?
    var upgradeCarAction: (() -> Void)?
    var repostCancelledCarAction: (() -> Void)?
    var acceptOfferAction: (() -> Void)?
    var declineOfferAction: (() -> Void)?
    var viewAllStatsAction: (() -> Void)?
    var viewAllLeadsAction: (() -> Void)?
    var optimizeAction: (() -> Void)?
    var viewCarAction: (() -> Void)?
    var contactUsAction: (() -> Void)?
    var viewAllBoostersAction: (() -> Void)?
    var viewReceivedOffersAction: (() -> Void)?
    var viewViewingRequestsAction: (() -> Void)?
    var viewChatMessagesAction: (() -> Void)?
    var boostCarAction: ((BoostServices?, Bool) -> Void)?
    private var isMicroDealer: Bool = false
    
    private var hostingController: UIHostingController<NewSelfServiceSectionsScreenTVCView>?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func configureCell(
        carStatus: CarStatusModel?,
        upgradeCarAction: (() -> Void)?,
        repostCancelledCarAction: (() -> Void)?,
        acceptOfferAction: (() -> Void)?,
        declineOfferAction: (() -> Void)?,
        viewAllStatsAction: (() -> Void)?,
        viewAllLeadsAction: (() -> Void)?,
        optimizeAction: (() -> Void)?,
        viewCarAction: (() -> Void)?,
        viewAllBoostersAction: (() -> Void)?,
        contactUsAction: (() -> Void)?,
        viewReceivedOffersAction: (() -> Void)?,
        viewViewingRequestsAction: (() -> Void)?,
        viewChatMessagesAction: (() -> Void)?,
        boostCarAction: ((BoostServices?, Bool) -> Void)?,
    	isMicroDealer: Bool
    ) {
        self.carStatus = carStatus
        self.upgradeCarAction = upgradeCarAction
        self.repostCancelledCarAction = repostCancelledCarAction
        self.acceptOfferAction = acceptOfferAction
        self.declineOfferAction = declineOfferAction
        self.viewAllStatsAction = viewAllStatsAction
        self.viewAllLeadsAction = viewAllLeadsAction
        self.optimizeAction = optimizeAction
        self.viewCarAction = viewCarAction
        self.viewAllBoostersAction = viewAllBoostersAction
        self.contactUsAction = contactUsAction
        self.viewReceivedOffersAction = viewReceivedOffersAction
        self.viewViewingRequestsAction = viewViewingRequestsAction
        self.viewChatMessagesAction = viewChatMessagesAction
        self.boostCarAction = boostCarAction
        self.isMicroDealer = isMicroDealer
        self.setupHostingController()
    }
    
    private func setupHostingController() {
        let swiftUIView = NewSelfServiceSectionsScreenTVCView(
            carStatus: self.carStatus,
            upgradeCarAction: self.upgradeCarAction,
            repostCancelledCarAction: self.repostCancelledCarAction,
            acceptOfferAction: self.acceptOfferAction,
            declineOfferAction: self.declineOfferAction,
            viewAllStatsAction: self.viewAllStatsAction,
            viewAllLeadsAction: self.viewAllLeadsAction,
            optimizeAction: self.optimizeAction,
            viewCarAction: self.viewCarAction,
            viewAllBoostersAction: self.viewAllBoostersAction,
            contactUsAction: self.contactUsAction,
            viewReceivedOffersAction: viewReceivedOffersAction,
            viewViewingRequestsAction: viewViewingRequestsAction,
            viewChatMessagesAction: viewChatMessagesAction,
            boostCarAction: boostCarAction,
            isMicroDealer: self.isMicroDealer
        )
        hostingController = UIHostingController(rootView: swiftUIView)
        
        guard let hostingController = hostingController else { return }
        
        contentView.addSubview(hostingController.view)
        
        hostingController.view.translatesAutoresizingMaskIntoConstraints = false
        hostingController.view.semanticContentAttribute = !LanguageHelper.isEnglish ? .forceRightToLeft : .forceLeftToRight
        NSLayoutConstraint.activate([
            hostingController.view.topAnchor.constraint(equalTo: contentView.topAnchor),
            hostingController.view.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            hostingController.view.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            hostingController.view.trailingAnchor.constraint(equalTo: contentView.trailingAnchor)
        ])
    }
}

struct NewSelfServiceSectionsScreenTVCView: View {
    @State private var isExpandedPaperwork: Bool = false
    
    var carStatus: CarStatusModel?
    var upgradeCarAction: (() -> Void)?
    var repostCancelledCarAction: (() -> Void)?
    var acceptOfferAction: (() -> Void)?
    var declineOfferAction: (() -> Void)?
    var viewAllStatsAction: (() -> Void)?
    var viewAllLeadsAction: (() -> Void)?
    var optimizeAction: (() -> Void)?
    var viewCarAction: (() -> Void)?
    var viewAllBoostersAction: (() -> Void)?
    var contactUsAction: (() -> Void)?
    var viewReceivedOffersAction: (() -> Void)?
    var viewViewingRequestsAction: (() -> Void)?
    var viewChatMessagesAction: (() -> Void)?
    var boostCarAction: ((BoostServices?, Bool) -> Void)?
    var isMicroDealer: Bool = false
    
    private struct CurrencyText: View {
        let amount: String
        var body: some View {
            HStack(spacing: 4) {
                if LanguageHelper.isEnglish {
                    Text(amount)
                    Text("KWD")
                } else {
                    Text("د.ك")
                    Text(amount)
                }
            }
            .environment(\.layoutDirection, .leftToRight)
        }
    }

    private struct MileageText: View {
        let km: String
        var body: some View {
            HStack(spacing: 4) {
                if LanguageHelper.isEnglish {
                    Text(km)
                    Text("KM")
                } else {
                    Text("كم")
                    Text(km)
                }
            }
            .environment(\.layoutDirection, .leftToRight)
        }
    }
    
    private func sectionDivider(height: CGFloat = 8) -> some View {
        VStack(spacing: 0) {
            Divider()
                .background(Color(red: 0.95, green: 0.96, blue: 0.97))
            Rectangle()
                .fill(Color(red: 0.95, green: 0.96, blue: 0.97))
                .frame(height: height)
            Divider()
                .background(Color(red: 0.95, green: 0.96, blue: 0.97))
        }
    }
    
    private func dottedLine(top: CGFloat = 16) -> some View {
        Rectangle()
            .foregroundColor(.clear)
            .frame(maxWidth: .infinity)
            .frame(height: 1)
            .overlay(
                Rectangle()
                    .inset(by: 0.5)
                    .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), style: StrokeStyle(lineWidth: 1, dash: [4, 4]))
            )
            .padding(.top, top)
            .padding(.leading, 16)
    }
    
    private func pendingReviewSection(needOffer: Bool) -> some View {
        ZStack(alignment: .topTrailing) {
            VStack(alignment: .leading, spacing: 16) {
                // Header
                HStack {
                    VStack(alignment: .leading, spacing: LanguageHelper.isEnglish ? 4 : -4) {
						Text(LanguageHelper.isEnglish ? "Received!" : "تم الاستلام!")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 16))
                            .padding(.leading, 16)
                        
						Text(needOffer ? "Your offer's on the way!".localized : LanguageHelper.isEnglish ? "Your car ad will be live shortly." : "سيتم نشر إعلان السيارة قريباً.")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 18))
                            .padding(.leading, 16)
                        
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(maxWidth: .infinity)
                            .frame(height: 1)
                            .overlay(
                                Rectangle()
                                    .inset(by: 0.5)
                                    .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), style: StrokeStyle(lineWidth: 1, dash: [4, 4]))
                            )
                            .padding(.top, LanguageHelper.isEnglish ? 16 : 10)
                            .padding(.leading, 16)
                            .padding(.trailing, 140)
                        
                        // What's next
                        Text(LanguageHelper.isEnglish ? "WHAT'S NEXT" : "ما التالي؟")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 12))
                            .foregroundColor(Color(uiColor: Colors.slateColor))
                            .padding(.leading, 16)
                            .padding(.top, LanguageHelper.isEnglish ? 16 : 10)
                    }
                    
                    Spacer()
                }
                
                // Status rows
                VStack(spacing: 16) {
                    HStack(spacing: 12) {
                        // Car icon
                        Image("ssdsdsd3")
                            .resizable()
                            .frame(width: 24, height: 24)
                        
                        // Text with partial bold
                        Text("Your car details are ".localized)
                            .foregroundColor(Color(uiColor: Colors.charcoalColor))
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14))
                        +
                        Text(needOffer ? "under review!".localized : LanguageHelper.isEnglish ? "pending review!" : "قيد المراجعة حاليا")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 14))
                            .foregroundColor(Color(uiColor: Colors.charcoalColor))
                        
                        Spacer()
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(Color(red: 1, green: 0.98, blue: 0.9))
                    .cornerRadius(12)
                    .shadow(color: Color(red: 1, green: 0.95, blue: 0.8), radius: 0, x: 0, y: 4)
                    .padding(.horizontal)
                    
                    if needOffer {
                        HStack(spacing: 12) {
                            // Dollar sign icon
                            Image("greensdasd")
                                .resizable()
                                .frame(width: 24, height: 24)
                            
                            VStack(alignment: .leading, spacing: 0) {
                                // Text with partial bold for hours
                                Text("We're getting you a cash offer within ".localized)
                                    .foregroundColor(Color(uiColor: Colors.charcoalColor))
                                    .font(.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14))
                                +
                                Text("24–48 hours.".localized)
                                    .foregroundColor(Color(uiColor: Colors.charcoalColor))
                                    .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 14))
                            }
                            
                            Spacer()
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color.white)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.gray.opacity(0.2), lineWidth: 0.75)
                        )
                        .shadow(color: Color(red: 0.95, green: 0.96, blue: 0.97), radius: 0, x: 0, y: 4)
                        .padding(.horizontal)
                    }
                }
            }
            .padding(.bottom, 16)
            .padding(.top, LanguageHelper.isEnglish ? 16 : 12)
            
            Image("dfdff33")
                .resizable()
                .frame(width: 110, height: 110)
                .padding(.trailing)
                .flipsForRightToLeftLayoutDirection(true)
                .zIndex(1)
        }
    }
    
    private func reviewSuccessSection() -> some View {
        ZStack(alignment: .topTrailing) {
            VStack(alignment: .leading, spacing: 16) {
                // Header
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(LanguageHelper.isEnglish ? "Sit back & relax," : "اجلس واسترح،")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 16))
                            .padding(.leading, 16)
                        
                        Text(LanguageHelper.isEnglish ? "Your offer's on the way!" : "سيصلك العرض النقدى قريبًا!")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 18))
                            .padding(.leading, 16)
                        
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(maxWidth: .infinity)
                            .frame(height: 1)
                            .overlay(
                                Rectangle()
                                    .inset(by: 0.5)
                                    .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), style: StrokeStyle(lineWidth: 1, dash: [4, 4]))
                            )
                            .padding(.top, 16)
                            .padding(.leading, 16)
                            .padding(.trailing, 140)
                        
                        // What's next
                        Text(LanguageHelper.isEnglish ? "WHAT'S NEXT" : "ما التالي؟")
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                    .weight(.semibold)
                            )
                            .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                            .padding(.leading, 16)
                            .padding(.top, 12)
                    }
                }
                
                // Status rows
                VStack(alignment: .leading, spacing: 0) {
                    HStack(spacing: 12) {
                        // Car icon
                        Image("sasdfsadcc")
                            .resizable()
                            .frame(width: 24, height: 24)
                        
                        // Text with partial bold
                        Text(LanguageHelper.isEnglish ? "Your car details have been " : "تمت مراجعة ")
                            .foregroundColor(Color(uiColor: Colors.charcoalColor))
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14))
                        +
                        Text(LanguageHelper.isEnglish ? "reviewed successfully." : "إعلانك بنجاح")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 14))
                            .foregroundColor(Color(uiColor: Colors.charcoalColor))
                        
                        Spacer()
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(Color.white)
                    .cornerRadius(16)
                    .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                    .padding(.horizontal)
                    
                    Rectangle()
                        .foregroundColor(.clear)
                        .frame(width: 2, height: 16)
                        .background(Color(red: 0.95, green: 0.96, blue: 0.97))
                    
                    HStack(spacing: 12) {
                        // Dollar sign icon
                        Image("greensdasd")
                            .resizable()
                            .frame(width: 24, height: 24)
                        
                        VStack(alignment: .leading, spacing: 0) {
                            // Text with partial bold for hours
                            Text(LanguageHelper.isEnglish ? "We're getting you a cash offer within\n" : "نعمل على توفير عرض نقدي لسيارتك خلال\n")
                                .foregroundColor(Color(uiColor: Colors.charcoalColor))
                                .font(.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14))
                            +
                            Text(LanguageHelper.isEnglish ? "24–48 hours." : "24–48 ساعة.")
                                .foregroundColor(Color(uiColor: Colors.charcoalColor))
                                .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 14))
                        }
                        
                        Spacer()
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 14)
                    .background(Color.white)
                    .cornerRadius(16)
                    .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                    .padding(.horizontal)
                }
            }
            .padding(.bottom, 16)
            .padding(.top, 16)
            
            if LanguageHelper.isEnglish {
                Image("dfdff33")
                    .resizable()
                    .frame(width: 110, height: 110)
                    .padding(.trailing, 16)
                    .padding(.top, 8)
                    .zIndex(1)
            } else {
                Image("jojdaod")
                    .resizable()
                    .frame(width: 110, height: 110)
                    .padding(.trailing, 16)
                    .padding(.top, 8)
                    .zIndex(1)
            }
        }
    }
    
    private func offerReceivedSection() -> some View {
        VStack {
            // Header
            VStack {
                HStack{
                    VStack(alignment: .leading, spacing: 4) {
                        Text(LanguageHelper.isEnglish ? "Great news," : "أخبار رائعة،")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 16))
                        
                        Text(LanguageHelper.isEnglish ? "Offer received!" : "وصلك عرض نقدي")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 18))
                        
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(maxWidth: .infinity)
                            .frame(height: 1)
                            .overlay(
                                Rectangle()
                                    .inset(by: 0.5)
                                    .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), style: StrokeStyle(lineWidth: 1, dash: [4, 4]))
                            )
                            .padding(.top, 16)
                        
                        HStack {
                            Text(LanguageHelper.isEnglish ? "Offer valid until" : "العرض سارٍ لمدة")
                                .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 12))
                                .foregroundColor(Color(Colors.slateColor))
                            
                            Text(hoursRemainingText(from: carStatus?.offerDetails?.OfferExpireDate ?? ""))
                                .font(
                                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                        .weight(.semibold)
                                )
                                .fixedSize(horizontal: true, vertical: false)

                                .padding(.horizontal, 12)
                                .padding(.vertical, 4)
                                .background(Color(UIColor.systemYellow).opacity(0.2))
                                .cornerRadius(16)
                            
                            Spacer(minLength: 0)
                        }
                        .padding(.top, 12)
                    }
                    .padding(.leading, 16)
                    .padding(.top, 24)
                    
                    Spacer()
                    
                    Image("acdhd66")
                        .resizable()
                        .frame(width: 110, height: 110)
                        .padding(.trailing, 16)
                        .flipsForRightToLeftLayoutDirection(true)
                }
            }
            
            // Offer details
            VStack(spacing: 0) {
                HStack {
                    Text(LanguageHelper.isEnglish ? "You've received a cash offer from Motorgy!" : "لقد تلقيت عرضًا نقديًا من موتورجي!")
                        .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 14))
                        .foregroundStyle(Color(uiColor: Colors.charcoalColor))
                    
                    Spacer(minLength: 0)
                    
                    CurrencyText(amount: carStatus?.carDetails?.SoldPrice?.withCommas() ?? "")
                        .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 14))
                        .foregroundStyle(Color(uiColor: Colors.charcoalColor))
                }
                
                HStack(spacing: 12) {
                    Button {
                        declineOfferAction?()
                    } label: {
                        Text(LanguageHelper.isEnglish ? "Decline" : "رفض")
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                    .weight(.bold)
                            )
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, LanguageHelper.isEnglish ? 12 : 8)
                            .background(Color(red: 1, green: 0.96, blue: 0.96))
                            .foregroundColor(Color(UIColor.systemRed))
                            .cornerRadius(8)
                    }
                    
                    Button {
                        acceptOfferAction?()
                    } label: {
                        Text(LanguageHelper.isEnglish ? "Accept" : "قبول")
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                    .weight(.bold)
                            )
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, LanguageHelper.isEnglish ? 12 : 8)
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(8)
                    }
                }
                .padding(.vertical, 16)
            }
            .padding(.top, 12)
            .padding(.horizontal, 16)
            .background(.white)
            .cornerRadius(12)
            .shadow(color: Color(red: 0.95, green: 0.96, blue: 0.97), radius: 0, x: 0, y: 4)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .inset(by: 0.5)
                    .stroke(Color(red: 0.95, green: 0.96, blue: 0.97), lineWidth: 1)
            )
            .padding(LanguageHelper.isEnglish ? 16 : 8)
        }
    }
    
    private func offerAcceptedSection() -> some View {
        VStack(alignment: .leading) {
            // Header
            VStack{
                HStack {
                    VStack(alignment: .leading, spacing: 0) {
                        Text(LanguageHelper.isEnglish ? "Great news," : "أخبار رائعة،")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 16))
                            .padding(.leading, 16)
                        
                        Text(LanguageHelper.isEnglish ? "Offer Accepted!🎉" : "تم قبول العرض! 🎉")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 18))
                            .padding(.leading, 16)
                        
                        CurrencyText(amount: carStatus?.carDetails?.SoldPrice?.withCommas() ?? "")
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                    .weight(.bold)
                            )
                            .padding(.horizontal, 32)
                            .padding(.vertical, 12)
                            .background(Color.white)
                            .cornerRadius(12)
                            .shadow(color: Color(red: 0.95, green: 0.96, blue: 0.97), radius: 0, x: 0, y: 4)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .inset(by: 0.5)
                                    .stroke(Color(red: 0.95, green: 0.96, blue: 0.97), lineWidth: 1)
                                
                            )
                            .padding(.leading, 16)
                            .padding(.top, 16)
                    }
                    
                    Spacer()
                    
                    Image("cxcxc343")
                        .resizable()
                        .frame(width: 110, height: 110)
                        .padding(.trailing, 16)
                }
            }
            
            dottedLine()
                .padding(.trailing, 16)
            
            // What's next
            Text(LanguageHelper.isEnglish ? "WHAT'S NEXT" : "ما التالي؟")
                .font(
                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                        .weight(.semibold)
                )
                .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                .padding(.leading, 16)
                .padding(.vertical, 12)
            
            HStack {
                // Pickup info
                Text(LanguageHelper.isEnglish ? "We'll contact you " : "سنتواصل معك ")
                    .font(
                        Font
                            .custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                            .weight(.regular)
                    )
                +
                Text(LanguageHelper.isEnglish ? "ASAP " : "في أقرب وقت ")
                    .font(
                        Font
                            .custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                            .weight(.bold)
                    )
                +
                Text(LanguageHelper.isEnglish ? "to schedule a pickup and finalize all paperwork." : "لتحديد موعد لأخذ السيارة وإنهاء جميع الأوراق.")
                    .font(
                        Font
                            .custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                            .weight(.regular)
                    )
            }
            .font(.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14))
            .padding(.leading, 16)
            .padding(.bottom, 8)
            
            // Paperwork section
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Image("Filesaa")
                        .resizable()
                        .frame(width: 20, height: 20)
                    
                    Text(LanguageHelper.isEnglish ? "Required documents" : "الأوراق المطلوبة")
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                .weight(.bold)
                        )
                        .foregroundColor(Color(red: 0.07, green: 0.07, blue: 0.07))
                    
                    Spacer()
                    
                    Image(systemName: isExpandedPaperwork ? "chevron.up" : "chevron.down")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    withAnimation {
                        isExpandedPaperwork.toggle()
                    }
                }
                
                if isExpandedPaperwork {
                    Rectangle()
                        .foregroundColor(.clear)
                        .frame(maxWidth: .infinity)
                        .frame(height: 1)
                        .overlay(
                            Rectangle()
                                .inset(by: 0.5)
                                .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), style: StrokeStyle(lineWidth: 1, dash: [4, 4]))
                        )
                    
                    VStack(alignment: .leading, spacing: 12) {
                        checklistItem(text: LanguageHelper.isEnglish ? "Copy of Civil ID" : "صورة البطاقة المدنية")
                        checklistItem(text: LanguageHelper.isEnglish ? "Copy of Car Registration (Car Book)" : "صورة دفتر السيارة")
                    }
                    .padding(.leading, 4)
                }
            }
            .padding(16)
            .background(Color(UIColor.hexStringToUIColor(hex: "#F9FAFB")))
            .cornerRadius(12)
            .padding(.horizontal, 16)
            .padding(.bottom, 10)
        }
        .padding(.top, 16)
        .padding(.horizontal, 8)
    }
    
    private func offerDeclinedSection(hasExpired: Bool) -> some View {
        ZStack(alignment: .topTrailing) {
            VStack {
                // Header
                VStack {
                    HStack {
                        VStack(alignment: .leading, spacing: LanguageHelper.isEnglish ? 0 : -4) {
                            Text(hasExpired ? LanguageHelper.isEnglish ? "Your cash offer" : "عرض السعر الخاص بك" : LanguageHelper.isEnglish ? "You've," : "لقد قُمت")
                                .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 16))
                                .padding(.leading, 16)
                            
                            Text(hasExpired ? LanguageHelper.isEnglish ? "has Expired!" : "إنتهت صلاحيته" : LanguageHelper.isEnglish ? "Declined the Offer!" : "برفض العرض النقدي")
                                .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 18))
                                .padding(.leading, 16)
                                .padding(.bottom, LanguageHelper.isEnglish ? 16 : 8)
                            
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(maxWidth: .infinity)
                                .frame(height: 1)
                                .overlay(
                                    Rectangle()
                                        .inset(by: 0.5)
                                        .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), style: StrokeStyle(lineWidth: 1, dash: [4, 4]))
                                )
                                .padding(.trailing, 140)
                                .padding(.leading, 16)
                            
                            // What's next
                            Text(LanguageHelper.isEnglish ? "WHAT'S NEXT" : "ما التالي؟")
                                .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 12))
                                .foregroundColor(Color(uiColor: Colors.slateColor))
                                .padding(.bottom, LanguageHelper.isEnglish ? 8 : 0)
                                .padding(.leading, 16)
                                .padding(.top, LanguageHelper.isEnglish ? 16 : 12)
                        }
                        
                        Spacer()
                    }
                }
                
                // Message
                HStack {
                    if hasExpired {
                        Text(LanguageHelper.isEnglish ? "Your car will be listed on Motorgy soon,\n giving more buyers the chance to contact you." : " سيتم نشر إعلان سيارتك قريبًا على موتورجي،\n مما يمنح المشترين المزيد من الفرص للاتصال بك.")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 14 : 12))
                            .padding(.leading, 16)
                    } else {
                        Text(LanguageHelper.isEnglish ? "Your car will be listed on the Motorgy soon to receive offers from buyers." : "سيتم نشر سيارتك على منصة موتورجي قريبًا لتلقي عروض من المشترين.")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 14 : 12))
                            .padding(.leading, 16)
                    }
                    
                    Spacer()
                }
                .padding(.bottom, LanguageHelper.isEnglish ? 4 : 1)
                .background(Color(UIColor.white))
                .cornerRadius(12)
            }
            .padding(.vertical, LanguageHelper.isEnglish ? 16 : 10)
            
            if hasExpired {
                Image(LanguageHelper.isEnglish ? "ssdweqrerw" : "sdsaf")
                    .resizable()
                    .frame(width: 110, height: 110)
                    .padding(.trailing, 16)
                    .padding(.top, 2)
                    .zIndex(1)
            } else {
                Image(LanguageHelper.isEnglish ? "MainDeclinesd" : "sdfsadfadsfeq")
                    .resizable()
                    .frame(width: 110, height: 110)
                    .padding(.trailing, 16)
                    .padding(.top, 2)
                    .zIndex(1)
            }
        }
    }
    
    private func adCanceledSection() -> some View {
        ZStack(alignment: .topTrailing) {
            VStack(alignment: .leading) {
                VStack(alignment: .leading) {
                    Text(LanguageHelper.isEnglish ? "Your car Ad," : "حالة السيارة")
                        .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 16))
                        .padding(.leading, 16)
                    
                    Text(LanguageHelper.isEnglish ? "is Canceled!" : "تم الإلغاء،")
                        .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 18))
                        .padding(.leading, 16)
                }
                .padding(.top, 16)
                .padding(.bottom, 12)
                
                Rectangle()
                    .foregroundColor(.clear)
                    .frame(maxWidth: .infinity)
                    .frame(height: 1)
                    .overlay(
                        Rectangle()
                            .inset(by: 0.5)
                            .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), style: StrokeStyle(lineWidth: 1, dash: [4, 4]))
                    )
                    .padding(.leading, 16)
                    .padding(.trailing, 126)
                
                HStack {
                    Text(LanguageHelper.isEnglish ? "No worries!" : "لا تقلق!")
                        .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 14))
                    +
                    Text(LanguageHelper.isEnglish ? " You can repost your car anytime. Fast and hassle-free!" : " يمكنك إعادة نشر سيارتك في أي وقت، بسرعة وبدون متاعب!")
                        .font(.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14))
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                
                Button {
                    repostCancelledCarAction?()
                } label: {
                    Text(LanguageHelper.isEnglish ? "Repost" : "إعادة نشر")
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                .weight(.bold)
                        )
                        .frame(maxWidth: .infinity)
                        .padding(12)
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 16)
            }
            
            Image("aasassadszz")
                .resizable()
                .frame(width: 110, height: 110)
                .flipsForRightToLeftLayoutDirection(true)
                .zIndex(1)
                .padding(.top, 2)
                .padding(.trailing, 16)
        }
    }
    
    private func carSoldSection() -> some View {
        return VStack {
            // Header
            VStack {
                HStack {
                    VStack(alignment: .leading, spacing: 10) {
                        Text(LanguageHelper.isEnglish ? "Congratulations," : "مبروك،")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 16))
                            .padding(.leading, 16)
                        
                        Text(LanguageHelper.isEnglish ? "Your car is Sold 🎉": "لقد تم بيع سيارتك")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 18))
                            .padding(.leading, 16)
                        
                        CurrencyText(amount: carStatus?.carDetails?.SoldPrice?.withCommas() ?? "")
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                    .weight(.bold)
                            )
                            .padding(.horizontal, 32)
                            .padding(.vertical, 12)
                            .background(.white)
                            .cornerRadius(12)
                            .shadow(color: Color(red: 0.95, green: 0.96, blue: 0.97), radius: 0, x: 0, y: 4)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .inset(by: 0.5)
                                    .stroke(Color(red: 0.95, green: 0.96, blue: 0.97), lineWidth: 1)
                                
                            )
                            .padding(.leading, 16)
                    }
                    
                    Spacer()
                    
                    Image("CarSoldMainImageee")
                        .resizable()
                        .frame(width: 110, height: 110)
                        .padding(.trailing, 16)
                        .padding(.top, 8)
                        .flipsForRightToLeftLayoutDirection(true)
                }
            }
            
            dottedLine(top: 12)
                .padding(.trailing, 16)
            
            HStack {
                Text(LanguageHelper.isEnglish ? "We're here for you, every step of the way." : " نحن هنا لمساعدتك في جميع الخطوات.")
                    .font(.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14))
                    .padding(.leading, 16)
                
                Spacer()
            }
            .padding(.vertical, 12)
        }
    }
    
    private func checklistItem(text: String) -> some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: "checkmark")
                .font(.footnote)
                .foregroundColor(Color(uiColor: UIColor.hexStringToUIColor(hex: "#111111")))
            
            Text(text)
                .font(Font.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14))
            
            Spacer()
        }
    }
    
    private func StatItemView(icon: String, count: Int, label: String) -> some View {
        VStack(spacing: 8) {
            Image(icon)
                .resizable()
                .frame(width: 24, height: 24)
            
            Text("\(count)")
                .font(
                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                        .weight(.bold)
                )
                .kerning(0.042)
                .multilineTextAlignment(.center)
                .foregroundColor(Color(red: 0.07, green: 0.07, blue: 0.07))
            
            Text(label)
                .font(
                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                        .weight(.semibold)
                )
                .multilineTextAlignment(.center)
                .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
    }
    
    private func carDetailsSection() -> some View {
        VStack {
            Rectangle()
                .foregroundColor(.clear)
                .frame(maxWidth: .infinity)
                .frame(height: 1)
                .background(Color(red: 0.95, green: 0.96, blue: 0.97))
            
            HStack(spacing: 12) {
                // Car Image
                KFImage(URL(string: carStatus?.carDetails?.ImageUrl ?? ""))
                    .placeholder {
                        Image("no-image")
                            .resizable()
                            .frame(width: 80, height: 50)
                            .scaledToFit()
                    }
                    .resizable()
                    .frame(width: 80, height: 50)
                    .scaledToFit()
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                    .padding(.leading, 16)
                
                // Car Details
                VStack(alignment: .leading, spacing: 4) {
                    // Car Make and Model
                    Text(carStatus?.carDetails?.Title ?? "")
                        .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 12))
                        .lineLimit(1)
                        .foregroundColor(Color(uiColor: Colors.charcoalColor))
                    
                    // Car Specs
                    HStack(spacing: 12) {
                        // Year
                        Text(carStatus?.carDetails?.Year?.description ?? "")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 12))
                            .foregroundColor(Color(uiColor: Colors.charcoalColor))
                        
                        // Mileage
                        MileageText(km: carStatus?.carDetails?.Mileage?.withCommas() ?? "")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 12))
                            .foregroundColor(Color(uiColor: Colors.slateColor))
                        
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(width: 1, height: 16)
                            .background(Color(red: 0.92, green: 0.93, blue: 0.94))
                        
                        // Price
                        CurrencyText(amount: carStatus?.carDetails?.Price?.withCommas() ?? "")
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-Bold" : "Cairo-Bold", size: 12))
                            .foregroundColor(Color(uiColor: Colors.bluishColor))
                    }
                }
                
                Spacer()
            }
        }
    }
    
    private func carAdIsLive(isLive: Bool) -> some View {
        HStack {
            HStack(spacing: 8) {
                if isLive {
                    LottieView(animation: .named("Live"))
                        .playing(loopMode: .loop)
                        .animationSpeed(0.7)
                        .frame(width: 20, height: 20)
                    
                    Text(LanguageHelper.isEnglish ? "Your car ad is live!" : "إعلان سيارتك فعّال!")
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                .weight(.semibold)
                        )
                } else {
                    Image("ClockCounterClockwisesds")
                        .resizable()
                        .frame(width: 20, height: 20)
                    
                    Text(LanguageHelper.isEnglish ? "Your car ad is inactive!" : "إعلان سيارتك غير فعّال!")
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                .weight(.semibold)
                        )
                }
            }
            
            Spacer()
            
            if isLive {
                Button {
                    viewCarAction?()
                } label: {
                    Text(LanguageHelper.isEnglish ? "View car" : "عرض السيارة")
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                .weight(.semibold)
                        )
                        .foregroundColor(.black)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(.white)
                        .cornerRadius(6)
                        .overlay(
                            RoundedRectangle(cornerRadius: 6)
                                .inset(by: 0.5)
                                .stroke(Color(red: 0.95, green: 0.96, blue: 0.97), lineWidth: 1)
                        )
                }
            } else {
                Button {
                    repostCancelledCarAction?()
                } label: {
                    Text(LanguageHelper.isEnglish ? "Repost" : "إعادة نشر")
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                .weight(.bold)
                        )
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color.green)
                        .cornerRadius(4)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 6)
        .background(Color.white)
        .cornerRadius(8)
    }
    
    private func carLeads(receivedOffersCount: Int, viewingRequestsCount: Int, chatMessagesCount: Int) -> some View {
        VStack(spacing: 0) {
            HStack {
                Image("sdasdfasf")
                    .foregroundColor(.black)
                Text(LanguageHelper.isEnglish ? "Car leads" : "التفاعلات على السيارة")
                    .font(
                        Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 17)
                            .weight(.semibold)
                    )
                    .multilineTextAlignment(.leading)
                
                Spacer()
                
                Button {
                    viewAllLeadsAction?()
                } label: {
                    Text(LanguageHelper.isEnglish ? "View all" : "عرض الكل")
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                .weight(.semibold)
                        )
                        .multilineTextAlignment(.leading)
                        .foregroundColor(Color(uiColor: Colors.charcoalColor))
                        .padding(.vertical, 5)
                        .padding(.horizontal, 16)
                        .overlay(
                            RoundedRectangle(cornerRadius: 100)
                                .inset(by: 0.5)
                                .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), lineWidth: 1)
                        )
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.vertical, 16)
            
            HStack(spacing: 12) {
                // Received Offers
                LeadItemView(
                    icon: "rfgew",
                    title: LanguageHelper.isEnglish ? "Received\nOffers" : "العروض \n المستلمة",
                    hasNotification: receivedOffersCount > 0,
                    notificationCount: receivedOffersCount,
                    callBackAction: {
                        self.viewReceivedOffersAction?()
                    }
                )
                
                // Viewing Requests
                LeadItemView(
                    icon: "sfg",
                    title: LanguageHelper.isEnglish ? "Viewing\nRequests" : "طلبات \nالمعاينة",
                    hasNotification: viewingRequestsCount > 0,
                    notificationCount: viewingRequestsCount,
                    callBackAction: {
                        self.viewViewingRequestsAction?()
                    }
                )
                
                // Chat Messages
                LeadItemView(
                    icon: "adfd",
                    title: LanguageHelper.isEnglish ? "Chat\nMessages" : "رسائل \nالدردشة",
                    hasNotification: chatMessagesCount > 0,
                    notificationCount: chatMessagesCount,
                    callBackAction: {
                        self.viewChatMessagesAction?()
                    }
                )
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.bottom, 6)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.horizontal, 20)
        .padding(.bottom, 24)
        .background(Color.white)
    }
    
    private func carStats(views: Int, chats: Int, calls: Int) -> some View {
        VStack(spacing: 0) {
            HStack {
                Image("ChartBarsss")
                    .resizable()
                    .frame(width: 18, height: 18)
                
                Text(LanguageHelper.isEnglish ? "Car statistics" : "إحصاءات السيارة")
                    .font(
                        Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 17)
                            .weight(.semibold)
                    )
                
                Spacer()
                
                if calls > 0 || chats > 0 || views > 0 {
                    Button {
                        viewAllStatsAction?()
                    } label: {
                        Text(LanguageHelper.isEnglish ? "View all" : "عرض الكل")
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                    .weight(.semibold)
                            )
                            .foregroundColor(Color(uiColor: Colors.charcoalColor))
                            .padding(.horizontal, 16)
                            .padding(.vertical, 5)
                            .overlay(
                                RoundedRectangle(cornerRadius: 100)
                                    .inset(by: 0.5)
                                    .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), lineWidth: 1)
                                    .foregroundStyle(.clear)
                            )
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 16)
            
            HStack(spacing: 0) {
                if calls == 0 && chats == 0 && views == 0 {
                    ZStack {
                        Image("behinddsd")
                            .resizable()
                            .padding(.horizontal, 24)
                        
                        Image("dddfadg")
                            .resizable()
                            .padding(.horizontal, 24)
                        
                        Image("faef")
                            .resizable()
                            .padding(.horizontal, 24)
                        
                        Text(LanguageHelper.isEnglish ? "No car statistics available yet!": "لا توجد إحصائيات للسيارة بعد")
                            .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12))
                            .multilineTextAlignment(.center)
                            .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                            .padding(.vertical, 12)
                            .padding(.horizontal, 16)
                            .background(
                                Color(red: 0.98, green: 0.98, blue: 0.98)
                                    .cornerRadius(100)
                            )
                            .padding(.vertical, 40)
                    }
                } else {
                    StatItemView(icon: "GroupCalldd", count: calls, label: LanguageHelper.isEnglish ? "CALL" : "إتصالات")
                    
                    StatItemView(icon: "GroupChatss", count: chats, label: LanguageHelper.isEnglish ? "CHAT" : "دردشة")
                    
                    StatItemView(icon: "GroupViewsdsd", count: views, label: LanguageHelper.isEnglish ? "VIEW" : "مشاهدات")
                }
            }
        }
        .padding(.vertical, 12)
        .background(Color.white)
    }
    
    private func carSold() -> some View {
        HStack {
            HStack(spacing: 8) {
                Text(LanguageHelper.isEnglish ? "Congratulations, your car is sold 🎉": "مبروك، لقد تم بيع سيارتك 🎉")
                    .font(
                        Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                            .weight(.semibold)
                    )
            }
            
            Spacer()
        }
        .padding(.horizontal)
        .padding(.vertical, 12)
        .background(Color.white)
    }
    
    private func adDetailSection(upgradeCarCallback: (() -> Void)?) -> some View {
        VStack(spacing: 16) {
            HStack {
                Text(LanguageHelper.isEnglish ? "Ad details" : "تفاصيل الإعلان")
                    .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 16 : 14))
                    .foregroundColor(Color(uiColor: Colors.charcoalColor))
                
                Spacer()
            }
            .padding(.horizontal, 24)
            .padding(.top, 16)
            
            VStack(spacing: 16) {
                // Package type row
                HStack(alignment: .center) {
                    Text(LanguageHelper.isEnglish ? "Package type" : "نوع الباقة")
                        .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14))
                        .kerning(0.042)
                        .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                    
                    Spacer()
                    HStack(spacing: 4) {
                        if self.isMicroDealer {
                            Text(
                                "\(LanguageHelper.isEnglish ? "\(carStatus?.carDetails?.PackageName ?? "")\(carStatus?.carDetails?.BoostPackageName?.isEmpty == false ? " - \(carStatus?.carDetails?.BoostPackageName ?? "")" : "")" : "\(carStatus?.carDetails?.PackageName ?? "")\(carStatus?.carDetails?.BoostPackageName?.isEmpty == false ? " - \(carStatus?.carDetails?.BoostPackageName ?? "")" : "")")"
                            )
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                    .weight(.semibold)
                            )
                            .kerning(0.042)
                            .lineLimit(2)
                            .multilineTextAlignment(.trailing)
                            .foregroundColor(Color(red: 0.07, green: 0.07, blue: 0.07))
                        } else {
                            Text(
                                "\(LanguageHelper.isEnglish ? "Self-Service, \(carStatus?.carDetails?.PackageName ?? "")\(carStatus?.carDetails?.BoostPackageName?.isEmpty == false ? " - \(carStatus?.carDetails?.BoostPackageName ?? "")" : "")" : "الخدمة-الذاتية، \(carStatus?.carDetails?.PackageName ?? "")\(carStatus?.carDetails?.BoostPackageName?.isEmpty == false ? " - \(carStatus?.carDetails?.BoostPackageName ?? "")" : "")")"
                            )
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                    .weight(.semibold)
                            )
                            .kerning(0.042)
                            .lineLimit(2)
                            .multilineTextAlignment(.trailing)
                            .foregroundColor(Color(red: 0.07, green: 0.07, blue: 0.07))
                        }
                    }
                }
                
                DashedDivider()
                
                if let createdDate = carStatus?.carDetails?.CreatedDate {
                    HStack {
                        Text(LanguageHelper.isEnglish ? "Created date" : "تاريخ الإنشاء")
                            .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14))
                            .kerning(0.042)
                            .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                        
                        Spacer()
                        
                        Text(convertDateManually(createdDate, showWithoutTime: true))
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                    .weight(.semibold)
                            )
                            .kerning(0.036)
                            .multilineTextAlignment(.trailing)
                            .foregroundColor(Color(red: 0.07, green: 0.07, blue: 0.07))
                    }
                }
                
                if carStatus?.sellingStep == SellingStepSelfServiceCar.CarCancelled.rawValue {
                    if let expiredDate = carStatus?.carDetails?.ExpiredDate {
                        DashedDivider()
                        
                        HStack {
                            Text(LanguageHelper.isEnglish ? "Expired date" : "تاريخ الإنتهاء")
                                .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14))
                                .kerning(0.042)
                                .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                            
                            Spacer()
                            
                            Text(convertDateManually(expiredDate, showWithoutTime: true))
                                .font(
                                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                        .weight(.semibold)
                                )
                                .kerning(0.036)
                                .multilineTextAlignment(.trailing)
                                .foregroundColor(Color(uiColor: Colors.grapefruitColor))
                        }
                    }
                }
                
                if carStatus?.sellingStep == SellingStepSelfServiceCar.CancelledOnline.rawValue {
                    if let publishedDate = carStatus?.carDetails?.PublishDate {
                        DashedDivider()
                        
                        HStack {
                            Text(LanguageHelper.isEnglish ? "Publised date" : "تاريخ النشر")
                                .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14))
                                .kerning(0.042)
                                .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                            
                            Spacer()
                            
                            Text(convertDateManually(publishedDate, showWithoutTime: true))
                                .font(
                                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                        .weight(.semibold)
                                )
                                .kerning(0.036)
                                .multilineTextAlignment(.trailing)
                                .foregroundColor(Color(uiColor: Colors.charcoalColor))
                        }
                    }
                    
                    if let cancelDate = carStatus?.carDetails?.CancelledDate {
                        DashedDivider()
                        
                        HStack {
                            Text(LanguageHelper.isEnglish ? "Ad expires in" : "تاريخ الإنتهاء")
                                .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14))
                                .kerning(0.042)
                                .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                            
                            Spacer()
                            
                            Text("\(LanguageHelper.isEnglish ? "Cancelled in \(convertDateManually(cancelDate, showWithoutTime: true))" : "تم الإلغاء في \(convertDateManually(cancelDate, showWithoutTime: true))")")
                                .font(
                                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                        .weight(.semibold)
                                )
                                .kerning(0.036)
                                .multilineTextAlignment(.trailing)
                                .foregroundColor(Color(uiColor: Colors.grapefruitColor))
                        }
                    }
                }
                
                if carStatus?.sellingStep == SellingStepSelfServiceCar.CarExpired.rawValue || carStatus?.sellingStep == SellingStepSelfServiceCar.CarOnline.rawValue || carStatus?.sellingStep == SellingStepSelfServiceCar.CarSold.rawValue || carStatus?.sellingStep == SellingStepSelfServiceCar.SoldOnline.rawValue {
                    if let publishedDate = carStatus?.carDetails?.PublishDate {
                        DashedDivider()
                        
                        HStack {
                            Text(LanguageHelper.isEnglish ? "Publised date" : "تاريخ النشر")
                                .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14))
                                .kerning(0.042)
                                .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                            
                            Spacer()
                            
                            Text(convertDateManually(publishedDate, showWithoutTime: true))
                                .font(
                                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                        .weight(.semibold)
                                )
                                .kerning(0.036)
                                .multilineTextAlignment(.trailing)
                                .foregroundColor(Color(uiColor: Colors.charcoalColor))
                        }
                    }
                    
                    if let expiredDate = carStatus?.carDetails?.ExpiredDate {
                        DashedDivider()
                        
                        HStack {
                            Text(LanguageHelper.isEnglish ? "Expired date" : "تاريخ الإنتهاء")
                                .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14))
                                .kerning(0.042)
                                .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                            
                            Spacer()
                            
                            Text(convertDateManually(expiredDate, showWithoutTime: true))
                                .font(
                                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                        .weight(.semibold)
                                )
                                .kerning(0.036)
                                .multilineTextAlignment(.trailing)
                                .foregroundColor(Color(uiColor: Colors.grapefruitColor))
                        }
                    }
                }
            }
            .cornerRadius(8)
            .padding(16)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .inset(by: 0.5)
                    .stroke(Color(red: 0.95, green: 0.96, blue: 0.96), lineWidth: 1)
            )
            .padding(.horizontal, 24)
            .padding(.bottom, carStatus?.isUpgradeEnabled ?? false ? 16 : 24)
            
            if carStatus?.isUpgradeEnabled ?? false == true {
                // Upgrade section
                VStack(spacing: 16) {
                    HStack(alignment: .top) {
                        Image("saDASD")
                            .resizable()
                            .frame(width: 32, height: 32)
                        
                        VStack(spacing: 9) {
                            Text(LanguageHelper.isEnglish ? "Let us sell for you!" : "دعنا نبيع لك سيارتك!")
                                .font(
                                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                        .weight(.semibold)
                                )
                                .foregroundColor(Color(red: 0.07, green: 0.07, blue: 0.07))
                            
                            Text(LanguageHelper.isEnglish ? "Upgrade to Concierge" : "الترقية إلى خدمة الكونسيرج")
                                .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12))
                                .kerning(0.036)
                                .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing, spacing: 12) {
                            CurrencyText(amount: carStatus?.upgradeObjectDetail?.DiscountPrice?.formattedWithWithoutFraction() ?? "")
                                .font(
                                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                        .weight(.bold)
                                )
                                .multilineTextAlignment(.trailing)
                                .foregroundColor(Color(red: 1, green: 0.27, blue: 0.23))
                            
                            if #available(iOS 16.0, *) {
                                CurrencyText(amount: carStatus?.upgradeObjectDetail?.Price?.formattedWithWithoutFraction() ?? "")
                                    .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12))
                                    .strikethrough()
                                    .multilineTextAlignment(.trailing)
                                    .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                            } else {
                                // Fallback on earlier versions
                            }
                        }
                    }
                    .padding(.horizontal, 24)
                    
                    Button {
                        upgradeCarCallback?()
                    } label: {
                        Text(LanguageHelper.isEnglish ? "Upgrade" : "ترقية")
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 10)
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                    .weight(.bold)
                            )
                            .foregroundColor(.green)
                            .background(Color.green.opacity(0.15))
                            .cornerRadius(8)
                    }
                    .padding(.horizontal, 24)
                }
                .padding(.vertical, 16)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .inset(by: 0.5)
                        .stroke(Color(red: 0.95, green: 0.96, blue: 0.96), lineWidth: 1)
                )
                .padding(.bottom, 24)
                .padding(.horizontal, 24)
            }
        }
        .background(Color(UIColor.white))
        .cornerRadius(12)
    }
    
    private func carAdCancelled(repostCarCancelledCallback: (() -> Void)?) -> some View {
        VStack {
            Rectangle()
                .foregroundColor(.clear)
                .frame(maxWidth: .infinity)
                .frame(height: 1)
                .background(Color(red: 0.95, green: 0.96, blue: 0.97))
            
            HStack {
                HStack(spacing: 8) {
                    Image("dafda")
                        .resizable()
                        .frame(width: 18, height: 18)
                    
                    Text(LanguageHelper.isEnglish ? "Your car Ad is cancelled!" : "تم الغاء إعلان سيارتك!")
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                .weight(.semibold)
                        )
                }
                
                Spacer()
                
                Button(action: {
                    repostCarCancelledCallback?()
                }) {
                    Text(LanguageHelper.isEnglish ? "Repost" : "إعادة نشر")
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                .weight(.bold)
                        )
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color(uiColor: Colors.shamrockColor))
                        .cornerRadius(6)
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 12)
            .background(Color.white)
            .cornerRadius(8)
        }
    }
    
    private func LeadItemView(icon: String, title: String, hasNotification: Bool, notificationCount: Int, callBackAction: @escaping () -> Void) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(icon)
                    .font(.title2)
                    .frame(width: 40, height: 40, alignment: .leading)
                
                if hasNotification {
                    ZStack {
                        Circle()
                            .fill(Color.red)
                            .frame(width: 16, height: 16)
                        
                        Text("\(notificationCount)")
                            .font(.system(size: 10, weight: .bold))
                            .foregroundColor(.white)
                    }
                }
                
                Spacer()
            }
            
            HStack {
                Text(title)
                    .font(
                        Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                            .weight(.semibold)
                    )
                    .lineLimit(nil)
                    .multilineTextAlignment(.leading)
                    .fixedSize(horizontal: false, vertical: true)
                
                Spacer()
                
                if LanguageHelper.isEnglish {
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                } else {
                    Image(systemName: "chevron.left")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(16)
        .background(.white)
        .cornerRadius(12)
        .shadow(color: Color(red: 0.95, green: 0.96, blue: 0.97), radius: 0, x: 0, y: 4)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .inset(by: 0.5)
                .stroke(Color(red: 0.95, green: 0.96, blue: 0.97), lineWidth: 1)
        )
        .onTapGesture {
            callBackAction()
        }
    }
    
    private func carBoosts() -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 12) {
                Image("rtrewewqrf")
                    .resizable()
                    .frame(width: 44, height: 44)
                
                VStack(alignment: .leading, spacing: 8) {
                    Text(LanguageHelper.isEnglish ? "Boost car sale!" : "سرّع عملية البيع")
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 16)
                                .weight(.semibold)
                        )
                    
                    Text(LanguageHelper.isEnglish ? "Increase visibility & attract more buyers" : "ارفع المشاهدات واجذب المزيد من المشترين")
                        .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12))
                        .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                }
            }
            
            Rectangle()
                .foregroundColor(.clear)
                .frame(height: 1)
                .overlay(
                    Rectangle()
                        .inset(by: 0.5)
                        .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), style: StrokeStyle(lineWidth: 1, dash: [4, 4]))
                )
                .padding(.bottom, 12)
            
            /// if activated -> show one (the activated)
            /// if expired -> show two (the expired and another one)
            /// if neither -> show two normally
            if let isActivatedPackage = self.carStatus?.boostServices?.first(where: { $0.IsActive ?? false }) {
                BoostOptionView(boostServices: isActivatedPackage)
                    .padding(.bottom, 12)
            } else {
                BoostOptionView(boostServices: self.carStatus?.boostServices?.first)
                    .padding(.bottom, 12)
                
                if self.carStatus?.boostServices?.count ?? 0 > 1 {
                    BoostOptionView(boostServices: self.carStatus?.boostServices?[1])
                        .padding(.bottom, 12)
                }
            }
        }
        .background(.white)
        .padding(24)
    }
    
    private func BoostOptionView(boostServices: BoostServices?) -> some View {
        ZStack(alignment: .top) {
            HStack {
                Text(boostServices?.Title ?? "")
                    .font(
                        Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                            .weight(.semibold)
                    )
                    .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                    .padding(.horizontal, 10)
                    .padding(.vertical, 6)
                    .background(Color.white)
                    .cornerRadius(100)
                    .overlay(
                        RoundedRectangle(cornerRadius: 100)
                            .inset(by: 0.5)
                            .stroke(Color(red: 0.92, green: 0.93, blue: 0.94), lineWidth: 1)
                    )
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.top, -11)
            .zIndex(1)
            
            VStack {
                HStack {
                    VStack(alignment: .leading, spacing: 8) {
                        ForEach(boostServices?.Boosters ?? [], id: \.self) { booster in
                            HStack(alignment: .top, spacing: 8) {
                                KFImage(URL(string: booster.icon ?? ""))
                                    .placeholder {
                                        Image("no-image")
                                            .resizable()
                                            .frame(width: 24, height: 24)
                                    }
                                    .resizable()
                                    .frame(width: 24, height: 24)
                                
                                Text(booster.description ?? "")
                                    .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14))
                                    .foregroundStyle(.black)
                            }
                        }
                    }
                    .padding(.top, 12)
                    
                    Spacer()
                    
                    VStack(spacing: 8) {
                        if boostServices?.IsActive == false && boostServices?.IsExpired == false {
                            CurrencyText(amount: boostServices?.Price?.formattedWithWithoutFraction() ?? "")
                                .font(
                                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                        .weight(.bold)
                                )
                                .foregroundColor(Color(uiColor: Colors.charcoalColor))
                            
                            Button {
                                boostCarAction?(boostServices, false)
                            } label: {
                                Text(LanguageHelper.isEnglish ? "Boost car" : "ميّز سيارتك")
                                    .font(
                                        Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                            .weight(.semibold)
                                    )
                                    .foregroundColor(.blue)
                                    .padding(.vertical, 7)
                                    .padding(.horizontal, 12)
                                    .background(Color(red: 0.95, green: 0.97, blue: 1))
                                    .cornerRadius(6)
                            }
                        } else if boostServices?.IsExpired == true {
                            HStack {
                                CurrencyText(amount: boostServices?.Price?.formattedWithWithoutFraction() ?? "")
                                    .font(
                                        Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                            .weight(.bold)
                                    )
                                    .foregroundColor(Color(uiColor: Colors.charcoalColor))
                            }
                        }
                    }
                }
                
                if boostServices?.IsActive ?? false {
                    HStack {
                        Image("Checksdsd")
                            .resizable()
                            .frame(width: 14, height: 14)
                        
                        Text(LanguageHelper.isEnglish ? "Activated" : "فعّال")
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                    .weight(.semibold)
                            )
                            .foregroundColor(Color(red: 0, green: 0.69, blue: 0.28))
                        
                        Spacer()
                        
                        Text(LanguageHelper.isEnglish ? "Expires in " : "ينتهي خلال ")
                            .font(Font.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 12))
                            .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                        
                        Text(LanguageHelper.isEnglish ? "\(boostServices?.ExpiresInDays ?? 0) days" : "\(boostServices?.ExpiresInDays ?? 0) يومًا")
                            .font(Font.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-Bold", size: 12))
                            .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                    }
                    .padding(8)
                    .cornerRadius(100)
                    .overlay(
                        RoundedRectangle(cornerRadius: 100)
                            .inset(by: 0.25)
                            .stroke(Color(red: 0.9, green: 0.97, blue: 0.93), lineWidth: 0.5)
                    )
                    .padding(.bottom, 8)
                } else if boostServices?.IsExpired ?? false {
                    HStack {
                        Text(LanguageHelper.isEnglish ? "Expired in \(convertDateManually(boostServices?.ExpirationDate ?? "", showWithoutTime: true))" : "انتهت في \(convertDateManually(boostServices?.ExpirationDate ?? "", showWithoutTime: true))")
                            .font(
                                Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                    .weight(.semibold)
                            )
                            .kerning(0.036)
                            .multilineTextAlignment(.center)
                            .foregroundColor(Color(red: 1, green: 0.27, blue: 0.23))
                        
                        Spacer()
                        
                        Button {
                            boostCarAction?(boostServices, true)
                        } label: {
                            Text(LanguageHelper.isEnglish ? "Renew" : "تجديد")
                                .font(
                                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                                        .weight(.semibold)
                                )
                                .foregroundStyle(.white)
                                .padding(.vertical, 5)
                                .padding(.horizontal, 16)
                                .background(Color(red: 0, green: 0.69, blue: 0.28))
                                .cornerRadius(12)
                        }
                    }
                    .padding(8)
                    .cornerRadius(100)
                    .overlay(
                        RoundedRectangle(cornerRadius: 100)
                            .inset(by: 0.25)
                            .stroke(Color(red: 0.95, green: 0.96, blue: 0.97), lineWidth: 0.5)
                        
                    )
                    .padding(.bottom, 8)
                }
            }
            .padding(.top, 16)
            .padding(.horizontal, 12)
            .padding(.bottom, 12)
            .background(.white)
            .cornerRadius(12)
            .shadow(
                color: boostServices?.IsActive ?? false ? Color(red: 0.9, green: 0.97, blue: 0.93) : boostServices?.IsExpired ?? false ? Color(red: 1, green: 0.93, blue: 0.92) : Color(red: 0.95, green: 0.96, blue: 0.97),
                radius: 0,
                x: 0,
                y: 4
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .inset(by: 0.5)
                    .stroke(
                        boostServices?.IsActive ?? false ? Color(red: 0.9, green: 0.97, blue: 0.93) : boostServices?.IsExpired ?? false ? Color(red: 1, green: 0.93, blue: 0.92) : Color(red: 0.95, green: 0.96, blue: 0.97),
                        lineWidth: 1
                    )
            )
        }
    }
    
    private func needHelpSection() -> some View {
        VStack(spacing: 16) {
            // Icon
            ZStack {
                Rectangle()
                    .foregroundColor(.clear)
                    .frame(width: 50, height: 50)
                    .background(Color(red: 1, green: 0.98, blue: 0.9))
                    .cornerRadius(12)
                
                Image("Lifebuoy 1")
                    .resizable()
                    .frame(width: 34, height: 34)
                    .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
            }
            
            // Title
            Text(LanguageHelper.isEnglish ? "Need help?" : " هل تحتاج إلى مساعدة؟")
                .font(
                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 16)
                        .weight(.semibold)
                )
                .foregroundColor(Color(red: 0.07, green: 0.07, blue: 0.07))
            
            // Subtitle
            Text(LanguageHelper.isEnglish ? "Our dedicated experts team\nis ready to help you." : "فريق الخبراء المخصص لدينا \nجاهز لمساعدتك.")
                .font(Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14))
                .multilineTextAlignment(.center)
                .foregroundColor(Color(red: 0.4, green: 0.44, blue: 0.52))
                .lineSpacing(2)
            
            // Contact us button
            Button {
                contactUsAction?()
            } label: {
                Text(LanguageHelper.isEnglish ? "Contact us" : "تواصل معنا")
                    .font(
                        Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                            .weight(.semibold)
                    )
                    .foregroundColor(Color(red: 0.19, green: 0.52, blue: 0.94))
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color(red: 0.95, green: 0.97, blue: 1))
                    .cornerRadius(8)
                    .padding(.horizontal, 24)
            }
        }
        .padding(.vertical, 24)
        .padding(.horizontal, 16)
        .background(Color.white)
    }
    
    @ViewBuilder
    private var pendingReview: some View {
        ScrollView {
            VStack {
                carDetailsSection()
                sectionDivider()
                pendingReviewSection(needOffer: carStatus?.needOffer ?? false)
                sectionDivider()
                adDetailSection(upgradeCarCallback: self.upgradeCarAction)
            }
        }
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    @ViewBuilder
    private var reviewed: some View {
        ScrollView {
            VStack {
                carDetailsSection()
                sectionDivider()
                if carStatus?.needOffer ?? false {
                    reviewSuccessSection()
                } else {
                    pendingReviewSection(needOffer: false)
                }
                sectionDivider()
                adDetailSection(upgradeCarCallback: self.upgradeCarAction)
            }
        }
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    @ViewBuilder
    private var offerReceived: some View {
        ScrollView {
            VStack {
                carDetailsSection()
                sectionDivider()
                offerReceivedSection()
                sectionDivider()
                adDetailSection(upgradeCarCallback: self.upgradeCarAction)
            }
        }
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    @ViewBuilder
    private var offerAccepted: some View {
        ScrollView {
            VStack {
                carDetailsSection()
                sectionDivider()
                offerAcceptedSection()
                sectionDivider()
                adDetailSection(upgradeCarCallback: self.upgradeCarAction)
                sectionDivider()
                    .padding(.top, 12)
                needHelpSection()
            }
        }
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    @ViewBuilder
    private var offerRejected: some View {
        ScrollView {
            VStack {
                carDetailsSection()
                sectionDivider()
                offerDeclinedSection(hasExpired: false)
                sectionDivider()
                adDetailSection(upgradeCarCallback: self.upgradeCarAction)
            }
        }
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    @ViewBuilder
    private var carCancelled: some View {
        ScrollView {
            VStack {
                carDetailsSection()
                sectionDivider()
                adCanceledSection()
                sectionDivider()
                adDetailSection(upgradeCarCallback: self.upgradeCarAction)
            }
        }
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    @ViewBuilder
    private var soldOnline: some View {
        ScrollView {
            VStack {
                carDetailsSection()
                Rectangle()
                    .foregroundColor(.clear)
                    .frame(maxWidth: .infinity)
                    .frame(height: 1)
                    .background(Color(red: 0.95, green: 0.96, blue: 0.97))
                carSold()
                sectionDivider()
                carLeads(
                    receivedOffersCount: self.carStatus?.carLeads?.ReceivedOffersCount ?? 0,
                    viewingRequestsCount: self.carStatus?.carLeads?.ViewingRequestsCount ?? 0,
                    chatMessagesCount: self.carStatus?.carLeads?.ChatMessagesCount ?? 0
                )
                sectionDivider()
                carStats(
                    views: self.carStatus?.carStatistics?.Views ?? 0,
                    chats: self.carStatus?.carStatistics?.Chats ?? 0,
                    calls: self.carStatus?.carStatistics?.Calls ?? 0
                )
                sectionDivider()
                adDetailSection(upgradeCarCallback: self.upgradeCarAction)
                sectionDivider()
                needHelpSection()
            }
        }
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    @ViewBuilder
    private var carOnline: some View {
        ScrollView {
            VStack {
                carDetailsSection()
                Rectangle()
                    .foregroundColor(.clear)
                    .frame(maxWidth: .infinity)
                    .frame(height: 1)
                    .background(Color(red: 0.95, green: 0.96, blue: 0.97))
                carAdIsLive(isLive: self.carStatus?.sellingStep == SellingStepSelfServiceCar.CarOnline.rawValue)
                Rectangle()
                    .foregroundColor(.clear)
                    .frame(maxWidth: .infinity)
                    .frame(height: 1)
                    .background(Color(red: 0.95, green: 0.96, blue: 0.97))
                carLeads(
                    receivedOffersCount: self.carStatus?.carLeads?.ReceivedOffersCount ?? 0,
                    viewingRequestsCount: self.carStatus?.carLeads?.ViewingRequestsCount ?? 0,
                    chatMessagesCount: self.carStatus?.carLeads?.ChatMessagesCount ?? 0
                )
                sectionDivider()
                carStats(
                    views: self.carStatus?.carStatistics?.Views ?? 0,
                    chats: self.carStatus?.carStatistics?.Chats ?? 0,
                    calls: self.carStatus?.carStatistics?.Calls ?? 0
                )
                sectionDivider()
                adDetailSection(upgradeCarCallback: self.upgradeCarAction)
                if let _ = self.carStatus?.boostServices {
                    sectionDivider()
                    carBoosts()
                }
            }
        }
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    @ViewBuilder
    private var carExpired: some View {
        ScrollView {
            VStack {
                carDetailsSection()
                carAdIsLive(isLive: false)
                sectionDivider()
                carLeads(
                    receivedOffersCount: self.carStatus?.carLeads?.ReceivedOffersCount ?? 0,
                    viewingRequestsCount: self.carStatus?.carLeads?.ViewingRequestsCount ?? 0,
                    chatMessagesCount: self.carStatus?.carLeads?.ChatMessagesCount ?? 0
                )
                sectionDivider()
                carStats(
                    views: self.carStatus?.carStatistics?.Views ?? 0,
                    chats: self.carStatus?.carStatistics?.Chats ?? 0,
                    calls: self.carStatus?.carStatistics?.Calls ?? 0
                )
                sectionDivider()
                adDetailSection(upgradeCarCallback: self.upgradeCarAction)
            }
        }
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    @ViewBuilder
    private var carOffline: some View {
        ScrollView {
            VStack {
                carDetailsSection()
                sectionDivider()
                carAdIsLive(isLive: self.carStatus?.sellingStep == SellingStepSelfServiceCar.CarOffline.rawValue || self.carStatus?.sellingStep == SellingStepSelfServiceCar.CarExpired.rawValue)
                sectionDivider()
                carLeads(
                    receivedOffersCount: self.carStatus?.carLeads?.ReceivedOffersCount ?? 0,
                    viewingRequestsCount: self.carStatus?.carLeads?.ViewingRequestsCount ?? 0,
                    chatMessagesCount: self.carStatus?.carLeads?.ChatMessagesCount ?? 0
                )
                sectionDivider()
                carStats(
                    views: self.carStatus?.carStatistics?.Views ?? 0,
                    chats: self.carStatus?.carStatistics?.Chats ?? 0,
                    calls: self.carStatus?.carStatistics?.Calls ?? 0
                )
                sectionDivider()
                adDetailSection(upgradeCarCallback: self.upgradeCarAction)
            }
        }
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    @ViewBuilder
    private var cancelledOnline: some View {
        ScrollView {
            VStack {
                carDetailsSection()
                carAdCancelled(repostCarCancelledCallback: repostCancelledCarAction)
                sectionDivider()
                carLeads(
                    receivedOffersCount: self.carStatus?.carLeads?.ReceivedOffersCount ?? 0,
                    viewingRequestsCount: self.carStatus?.carLeads?.ViewingRequestsCount ?? 0,
                    chatMessagesCount: self.carStatus?.carLeads?.ChatMessagesCount ?? 0
                )
                sectionDivider()
                carStats(
                    views: self.carStatus?.carStatistics?.Views ?? 0,
                    chats: self.carStatus?.carStatistics?.Chats ?? 0,
                    calls: self.carStatus?.carStatistics?.Calls ?? 0
                )
                sectionDivider()
                adDetailSection(upgradeCarCallback: self.upgradeCarAction)
            }
        }
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    @ViewBuilder
    private var carSoldView: some View {
        ScrollView {
            VStack {
                carDetailsSection()
                sectionDivider()
                carSoldSection()
                sectionDivider()
                adDetailSection(upgradeCarCallback: self.upgradeCarAction)
                sectionDivider()
                needHelpSection()
            }
        }
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    @ViewBuilder
    private var offerExpired: some View {
        ScrollView {
        	VStack {
                carDetailsSection()
                sectionDivider()
                offerDeclinedSection(hasExpired: true)
                sectionDivider()
                adDetailSection(upgradeCarCallback: self.upgradeCarAction)
            }
        }
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    var body: some View {
        switch self.carStatus?.sellingStep {
            case SellingStepSelfServiceCar.PendingReview.rawValue: pendingReview
            case SellingStepSelfServiceCar.Reviewed.rawValue: reviewed
            case SellingStepSelfServiceCar.OfferReceived.rawValue: offerReceived
            case SellingStepSelfServiceCar.OfferAccepted.rawValue: offerAccepted
            case SellingStepSelfServiceCar.OfferRejected.rawValue: offerRejected
            case SellingStepSelfServiceCar.CarCancelled.rawValue: carCancelled
            case SellingStepSelfServiceCar.SoldOnline.rawValue: soldOnline
            case SellingStepSelfServiceCar.CarOnline.rawValue: carOnline
            case SellingStepSelfServiceCar.CarExpired.rawValue: carExpired
            case SellingStepSelfServiceCar.CarOffline.rawValue: carOffline
            case SellingStepSelfServiceCar.CancelledOnline.rawValue: cancelledOnline
            case SellingStepSelfServiceCar.CarSold.rawValue: carSoldView
            case SellingStepSelfServiceCar.OfferExpired.rawValue: offerExpired
            default: EmptyView()
        }
    }
}

struct DashedDivider: View {
    var body: some View {
        Rectangle()
            .stroke(style: StrokeStyle(lineWidth: 1, dash: [4]))
            .foregroundColor(Color(red: 0.92, green: 0.93, blue: 0.94))
            .frame(height: 1)
            .frame(maxWidth: .infinity)
    }
}
