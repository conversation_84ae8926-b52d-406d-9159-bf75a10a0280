//
//  MicroDealerHeaderView.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 15/01/2025.
//  Copyright © 2025 <PERSON><PERSON>. All rights reserved.
//

import SwiftUI

struct MicroDealerHeaderView: View {
    let bundles: [BundleModelMyCars]
    let onBuyBundleTapped: () -> Void
    let onListCarTapped: (BundleModelMyCars) -> Void
    let onInfoIconTapped: (BundleModelMyCars) -> Void
    let onRenewTapped: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            headerSection
            
            if bundles.count == 1 {
                if let bundle = bundles.first {
                    BundleCardView(bundle: bundle, onListCarTapped: onListCarTapped, onInfoIconTapped: onInfoIconTapped, onRenewTapped: onRenewTapped)
                        .padding(.horizontal, 16)
                }
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(bundles, id: \.id) { bundle in
                            BundleCardView(bundle: bundle, onListCarTapped: onListCarTapped, onInfoIconTapped: onInfoIconTapped, onRenewTapped: onRenewTapped)
                        }
                    }
                    .padding(.horizontal, 16)
                }
            }
        }
        .background(Color.init(uiColor: UIColor.hexStringToUIColor(hex: "#F9FAFB")))
        .cornerRadius(12)
        .padding(.vertical, 16)
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
    
    private var headerSection: some View {
        HStack {
            Text(LanguageHelper.isEnglish ? "MY BUNDLES" : "باقاتي")
                .font(
                    Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                        .weight(.bold)
                )
                .foregroundColor(Color.init(uiColor: Colors.slateColor))
            
            Spacer()
            
            Button(action: onBuyBundleTapped) {
                Text(LanguageHelper.isEnglish ? "Buy a bundle" : "اشتر باقة")
                    .font(
                        Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                            .weight(.semibold)
                    )
                    .foregroundColor(Color.blue)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.blue, lineWidth: 1)
                    )
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
    }
}

struct BundleCardView: View {
    let bundle: BundleModelMyCars
    let onListCarTapped: (BundleModelMyCars) -> Void
    let onInfoIconTapped: (BundleModelMyCars) -> Void
    let onRenewTapped: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            bundleHeaderRow
            progressSection
            expirationAndActionRow
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 24)
        .background(.white)
        .cornerRadius(12)
        .shadow(color: Color(red: 0.95, green: 0.96, blue: 0.97), radius: 0, x: 0, y: 4)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .inset(by: 0.5)
                .stroke(Color(red: 0.95, green: 0.96, blue: 0.97), lineWidth: 1)
        )
        .padding(.bottom, 16)
    }
    
    private var bundleHeaderRow: some View {
        HStack(alignment: .center, spacing: 4) {
            HStack(spacing: 0) {
                Text(bundle.packageName ?? "")
                    .font(
                        Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                            .weight(.semibold)
                    )
                    .foregroundColor(.black)

                if let boostName = bundle.boostPackageName, !boostName.isEmpty, boostName != "<null>" {
                    Text(" + ")
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                .weight(.semibold)
                        )
                        .foregroundColor(.black)

                    Text(boostName)
                        .font(
                            Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
                                .weight(.semibold)
                        )
                        .foregroundColor(.black)
                        .background(alignment: .bottom) {
                            Color.yellow
                                .opacity(0.4)
                                .frame(height: 4)
                        }
                }
            }
            
            infoIcon
            
            Spacer()
            
            usageIndicator
        }
    }
    
    private var infoIcon: some View {
        Button(action: {
            onInfoIconTapped(bundle)
        }) {
            Image("packageInfoIcon")
                .resizable()
                .frame(width: 20, height: 20)
                .padding(4)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var usageIndicator: some View {
        // Create attributed string with bold used ads and regular total ads
        let usedAds = bundle.numberOfUsedAds ?? 0
        let totalAds = bundle.numberOfAds ?? 0
        
        var attributedString = AttributedString("\(usedAds)/\(totalAds)")
        
        // Make the used ads number bold
        if let usedRange = attributedString.range(of: "\(usedAds)") {
            attributedString[usedRange].font = Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12).weight(.bold)
        }
        
        // Make the slash and total ads regular
        if let slashTotalRange = attributedString.range(of: "/\(totalAds)") {
            attributedString[slashTotalRange].font = Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12).weight(.regular)
        }
        
        return Text(attributedString)
            .foregroundColor(.black)
            .padding(.horizontal, 5)
            .padding(.vertical, 2)
            .background(Color.init(uiColor: UIColor.hexStringToUIColor(hex: "#F2F4F7")))
            .clipShape(Capsule())
            .environment(\.layoutDirection, .leftToRight) // Force LTR for numbers
    }
    
    private var progressSection: some View {
        VStack(alignment: .leading, spacing: 4) {
            progressBar
        }
    }
    
    private var progressBar: some View {
        GeometryReader { geometry in
            if bundle.isExpired ?? false {
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.init(uiColor: Colors.grapefruitColor))
                        .frame(height: 2)
                        .cornerRadius(2)
                }
            } else {
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.init(uiColor: UIColor.hexStringToUIColor(hex: "#EAF5F8")))
                        .frame(height: 2)
                        .cornerRadius(2)
                    
                    HStack(spacing: 0) {
                        Rectangle()
                            .fill(Color.init(uiColor: Colors.bluishColor))
                            .frame(width: geometry.size.width * CGFloat(Double(bundle.numberOfUsedAds ?? 0) / Double(bundle.numberOfAds ?? 1)), height: 2)
                            .cornerRadius(2)
                            .zIndex(1)
                        
                        if bundle.numberOfRemainingAds ?? 0 > 0 {
                            Rectangle()
                                .fill(Color.init(uiColor: UIColor.hexStringToUIColor(hex: "#EAF5F8")))
                                .frame(width: geometry.size.width * CGFloat(Double(bundle.numberOfRemainingAds ?? 0) / Double(bundle.numberOfAds ?? 1)), height: 2)
                                .cornerRadius(2)
                                .zIndex(0)
                        }
                    }
                }
            }
        }
        .frame(height: 2)
    }
    
    private var expirationAndActionRow: some View {
        HStack {
            if bundle.isExpired ?? false {
                Text("\(LanguageHelper.isEnglish ? "Expired in \(convertDateManually(bundle.expirationDate ?? "", showWithoutTime: true))" : "تنتهي في \(convertDateManually(bundle.expirationDate ?? "", showWithoutTime: true))")")
                    .font(Font.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 12))
                    .foregroundColor(Color.init(uiColor: Colors.grapefruitColor))
            } else {
                Text("\(LanguageHelper.isEnglish ? "Valid until \(convertDateManually(bundle.expirationDate ?? "", showWithoutTime: true))" : "متاحة حتي \(convertDateManually(bundle.expirationDate ?? "", showWithoutTime: true))")")
                    .font(Font.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 12))
                    .foregroundColor(Color.init(uiColor: Colors.slateColor))
            }
                
			if bundle.isExpired ?? false {
				Spacer()
				
				Button {
					onRenewTapped()
					ConstantsValues.sharedInstance.renewingExpiredMicroDealerBundleId = bundle.id ?? 0
				} label: {
					Text(LanguageHelper.isEnglish ? "Renew" : "تجديد")
						.font(
							Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 12)
								.weight(.semibold)
						)
						.foregroundColor(.white)
						.padding(.horizontal, 16)
						.padding(.vertical, 8)
						.background(Color.init(uiColor: Colors.shamrockColor))
						.cornerRadius(8)
				}
			} else {
				Spacer()
				
				Button(action: { onListCarTapped(self.bundle) }) {
					Text(LanguageHelper.isEnglish ? "List a car" : "أضف سيارة")
						.font(Font.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 12))
						.foregroundColor(.blue)
						.padding(.horizontal, 16)
						.padding(.vertical, 8)
						.background(Color.blue.opacity(0.1))
						.cornerRadius(8)
				}
			}
        }
    }
}
